import os
import streamlit as st
import pandas as pd
import plotly.graph_objects as go
import plotly.express as px
from PIL import Image
import re
from datetime import datetime, timedelta, time
import Enc_Dec_Utility as encdec
def is_within_time_range(filename, start_time, end_time):
    # Extract time from filename using regex
    time_pattern = re.search(r'_(\d{2})_(\d{2})_(\d{2})\.jpg', filename)
    if not time_pattern:
        return False

    hour, minute, second = map(int, time_pattern.groups())
    image_time = datetime.strptime(f"{hour:02d}:{minute:02d}:{second:02d}", "%H:%M:%S").time()

    # Check if the image time is within the start and end time range
    return start_time <= image_time <= end_time

@st.cache_data
def load_images(image_folder, selected_date, start_time_frame, end_time_frame):
    date_pattern = selected_date.strftime("%d_%b_%Y").replace(' ', '_').replace('-', '_') + r'_(\d{2})_(\d{2})_(\d{2})\.jpg'
    images_in_time_frame = []
    
    for root, dirs, files in os.walk(image_folder):
        for image_file in files:
            match = re.search(date_pattern, image_file)
            if match:
                hour, minute, second = map(int, match.groups())
                if is_within_time_range(image_file, start_time_frame, end_time_frame):
                    images_in_time_frame.append((image_file, hour, minute, second))
                    
    images_in_time_frame.sort(key=lambda x: (x[1], x[2], x[3]))
    return images_in_time_frame

def show_image_viewer(image_folder, selected_date, start_time_frame, end_time_frame):
    # Ensure image_index exists in session state
    if 'image_index' not in st.session_state:
        st.session_state.image_index = 0

    # Load images
    images_in_time_frame = load_images(image_folder, selected_date, start_time_frame, end_time_frame)

    if not images_in_time_frame:
        st.write("No images found in the selected date and time frame.")
        return
    
    # Display image
    image_index = st.session_state.image_index
    selected_image = images_in_time_frame[image_index][0]
    date_folder = selected_date.strftime("%Y_%#m_%#d")
    image_path = os.path.join(image_folder, date_folder, selected_image)

    with Image.open(image_path) as img:
        st.image(img, caption=selected_image, use_column_width=True)

    return images_in_time_frame

def image_viewer_section(image_base_path):
    with st.expander("Image Viewer", expanded=True):
        # Date input for image viewer
        image_viewer_date = st.date_input("Select Date for Image Viewer", key="image_viewer_date")
        
        if not st.session_state.get('show_images', False):
            # Button to show images
            if st.button("Show Images"):
                st.session_state['show_images'] = True
        else:
            # Convert total seconds in a day to 45-second intervals
            intervals_in_a_day = (24 * 60 * 60) // 45
            interval_labels = [(datetime(2000, 1, 1) + timedelta(seconds=i * 45)).time().strftime("%H:%M:%S") for i in range(intervals_in_a_day)]
            
            # Add slider for time range selection
            start_interval, end_interval = st.select_slider(
                "Select Start and End Time",
                options=list(range(intervals_in_a_day)),
                value=(0, intervals_in_a_day - 1),
                format_func=lambda x: interval_labels[x],
                key="time_range"
            )

            # Convert selected intervals to start and end times
            start_time_seconds = start_interval * 45
            end_time_seconds = (end_interval + 1) * 45
            start_time = time((start_time_seconds // 3600) % 24, (start_time_seconds % 3600) // 60, start_time_seconds % 60)
            end_time = time((end_time_seconds // 3600) % 24, (end_time_seconds % 3600) // 60, end_time_seconds % 60)

            # Show image viewer
            images_in_time_frame = show_image_viewer(image_base_path, image_viewer_date, start_time, end_time)

            # Navigation buttons
            if images_in_time_frame:
                col1, col2, col3 = st.columns([1, 12, 1])
                with col1:
                    if st.button("Previous"):
                        st.session_state.image_index = (st.session_state.image_index - 1) % len(images_in_time_frame)
                with col3:
                    if st.button("Next"):
                        st.session_state.image_index = (st.session_state.image_index + 1) % len(images_in_time_frame)
                        
@st.cache_data
def load_image(image_path):
    # Optimize the image loading here if necessary (e.g., resizing, compression)
    return Image.open(image_path)
                        
def imageViewer(df):
    if 'image_index' not in st.session_state:
        st.session_state.image_index = 0

    if 'time_range' not in st.session_state:
        
        st.session_state.time_range = (df.iloc[0]['strTime'].to_pydatetime(), df.iloc[-1]['EndTime'].to_pydatetime())
        
    df['strTime'] = pd.to_datetime(df['strTime'])
    with st.expander("Select time range", expanded=True):
        if 'strTime' in df.columns:
            min_time = df.iloc[0]['strTime'].to_pydatetime()
        else:
            st.error("The 'strTime' column is missing from the DataFrame.")
            min_time = pd.Timestamp.now() - pd.DateOffset(days=30)
        
        if 'EndTime' in df.columns:
            max_time = df.iloc[-1]['EndTime'].to_pydatetime()
        else:
            st.error("The 'EndTime' column is missing from the DataFrame.")
            max_time = pd.Timestamp.now() + pd.DateOffset(days=30)

        # Set default end time to one hour from the min_time or the max_time, whichever is smaller
        default_end_time = min(min_time + timedelta(hours=0.3), max_time)

        # Handle NaT values
        if pd.isna(min_time):
            min_time = pd.Timestamp.now() - pd.DateOffset(days=30)
        if pd.isna(max_time):
            max_time = pd.Timestamp.now() + pd.DateOffset(days=30)
        if pd.isna(default_end_time):
            default_end_time = min_time + pd.DateOffset(days=1)

        # Convert to datetime
        min_time = min_time.to_pydatetime()
        max_time = max_time.to_pydatetime()
        default_end_time = default_end_time.to_pydatetime()
        time_range = st.slider("Select time range",
                            min_value=min_time, 
                            max_value=max_time, 
                            value=(min_time, default_end_time),  # Set default range from min_time to default_end_time
                            format="YYYY-MM-DD HH:mm:ss")

    # Filter the dataframe based on the selected time range
    filtered_df = df[(df['strTime'] >= time_range[0]) & (df['strTime'] <= time_range[1])]     

    # Fill empty 'Project' fields with "Other"
    df['Project'].replace({'': 'Other', 'nan': 'Other'}, inplace=True)
    # Sort by start time to organize the projects
    df = df.sort_values(by='strTime')
        # Timeline chart expander
    # with st.expander("Project Timeline Chart", expanded=True):
    #     # Assign black color to 'Break' projects and map colors for others
    #     colors = px.colors.qualitative.Plotly
    #     projects = df['Project'].unique()
    #     project_color_map = {'Break': '#FFFFFF'}  # Assign white color to 'Break'
    #     for i, project in enumerate(projects):
    #         if project not in project_color_map:
    #             project_color_map[project] = colors[i % len(colors)]
                
    #     # Determine the number of columns for the legend items
    #     num_cols_per_row = 6  # Adjust this to fit your layout
    #     num_legend_items = len(project_color_map)
    #     num_rows = (num_legend_items + num_cols_per_row - 1) // num_cols_per_row  # Calculate the number of rows needed

    #     # Create rows of columns to hold the legend items
    #     for i in range(num_rows):
    #         cols = st.columns(num_cols_per_row)
    #         start_idx = i * num_cols_per_row
    #         end_idx = start_idx + num_cols_per_row
    #         for col, (project, color) in zip(cols, list(project_color_map.items())[start_idx:end_idx]):
    #             # Use Markdown to display colored square and project name
    #             col.markdown(f"<span style='display: inline-block; width: 10px; height: 10px; background: {color}; margin-right: 3px;'></span>{project}", unsafe_allow_html=True)

    #     # Initialize a figure
    #     fig = go.Figure()

    #     # The Y value for all bars will be the same since we want a single grouped line
    #     y_value = 'Project Timeline'

    #     # Add a bar for each project entry
    #     for _, row in df.iterrows():
    #         project_name = row['Project']
    #         if pd.isna(project_name):
    #             project_name = 'Unknown Project'  # Or some default value
    #         # Calculate duration in milliseconds
    #         duration = (row['EndTime'] - row['strTime']).total_seconds() * 1000
    #         fig.add_trace(go.Bar(
    #             x=[duration],
    #             y=[y_value],
    #             base=row['strTime'],
    #             marker=dict(color=project_color_map.get(row['Project'], '#000000')),
    #             name=project_name,
    #             orientation='h',
    #             hoverinfo='text',
    #             hovertext=f"{row['Project']}: {row['strTime']} - {row['EndTime']}"
    #         ))

    #     # Update layout
    #     fig.update_layout(
    #         barmode='stack',
    #         xaxis={
    #             'type': 'date',
    #             'range': [filtered_df['strTime'].min(), filtered_df['EndTime'].max()]
    #         },
    #         yaxis={'visible': False, 'showticklabels': False},
    #         height=350,
    #         showlegend=False
    #     )

    #     # Display the chart in the Streamlit app
    #     st.plotly_chart(fig, use_container_width=True)

    # Filter unique values for the filters
    applications = list(filter(None, filtered_df['Application'].unique()))
    customers = list(filter(None, filtered_df['Customer'].unique()))
    projects = list(filter(None, filtered_df['Project'].unique()))
    
    with st.expander("Filter For Image", expanded=True):
        # Use st.multiselect for multiple select filters
        selected_applications = st.multiselect("Select Applications", applications, default=[])
        selected_customers = st.multiselect("Select Customers", customers, default=[])
        selected_projects = st.multiselect("Select Projects", projects, default=[])

    # Apply filters to the DataFrame
    if selected_applications or selected_customers or selected_projects:
        filtered_df = filtered_df[
            (filtered_df['Application'].isin(selected_applications)) |
            (filtered_df['Customer'].isin(selected_customers)) |
            (filtered_df['Project'].isin(selected_projects))
        ]
    else:
        filtered_df = df[(df['strTime'] >= time_range[0]) & (df['strTime'] <= time_range[1])]
    # Set the number of columns for the grid
    # num_columns = 1
    image_paths = filtered_df['pngFile'].unique()
    # Display the total number of images
    total_images = len(image_paths)
    st.info(f"Total images: {total_images}")
    
    # Create the grid of images
    if total_images > 1:
        st.session_state.image_index = st.slider("Select Image", 0, total_images - 1, st.session_state.image_index)
    else:
        # If there is only one image, set the index to 0 (or any desired default)
        st.session_state.image_index = 0

    if total_images > 0:
        with st.expander("View Image", expanded=True):
            with st.spinner("Loading images..."):
                image_path = image_paths[st.session_state.image_index]
                
                encdec.decrypt_file(image_path)
                image = load_image(image_path)
                encdec.encrypt_file(image_path)

                new_image = image.resize((2500, 800))
                st.image(new_image, caption=image_path, use_column_width=True)
        # Next and Previous buttons
        col1, col2, col3 = st.columns([1, 12, 1])
        with col1:
            if st.button("Previous", key="prev_button") and st.session_state.image_index > 0:
                st.session_state.image_index -= 1
        with col3:
            if st.button("Next", key="next_button") and st.session_state.image_index < total_images - 1:
                st.session_state.image_index += 1
    # # Create the grid of images
    # for i in range(0, total_images, num_columns):
    #     columns = st.columns(num_columns)
    #     for j in range(num_columns):
    #         if i + j < total_images:
    #             with columns[j]:
    #                 image_path = image_paths[i + j]
    #                 image = load_image(image_path)
    #                 with st.expander("View Image", expanded=True):
    #                     new_image = image.resize((2500,800))
    #                     st.image(new_image, caption=image_path, use_column_width=True)
    #                 # if st.button(f'View {image_path}', key=image_path):
    #                 #     with st.expander("View Image", expanded=True):
    #                 #         st.image(image, use_column_width=True)  # Image will expand to the width of the expander

# def show_image_viewer(image_folder, selected_date, images_to_display):
#     if 'image_index' not in st.session_state:
#         st.session_state.image_index = 0

#     if not images_to_display:
#         st.write("No images found in the selected date and time frame.")
#         return
    
#     # Display image
#     image_index = st.session_state.image_index
#     selected_image_time = images_to_display[image_index]
#     date_folder = selected_date.strftime("%Y_%#m_%#d")
#     image_folder_path = os.path.join(image_folder, date_folder)
    
#     # Create a regex pattern to search for the image file based on the timestamp
#     time_str = selected_image_time.strftime('%H_%M_%S')
#     time_pattern = re.compile(r'(\d{1,2})_(\d{1,2})_(\d{1,2})')
#     match = time_pattern.match(time_str)
#     if match:
#         hour, minute, second = match.groups()
#         image_filename_pattern = re.compile(rf'{hour}_{minute:0>2}_{second:0>2}.jpg')

#         # Search for the image file based on the timestamp
#         image_filename = None
#         for file in os.listdir(image_folder_path):
#             if image_filename_pattern.search(file):
#                 image_filename = file
#                 break

#         if image_filename is None:
#             st.error(f"No image found for the selected time: {selected_image_time.strftime('%H:%M:%S')}")
#             return

#         image_path = os.path.join(image_folder_path, image_filename)
#         with open(image_path, "rb") as file:
#             img = Image.open(file)
#             st.image(img, caption=selected_image_time.strftime('%H:%M:%S'), use_column_width=True)
#     else:
#         st.error("Invalid time format")

# def get_image_timestamps(image_folder, date):
#     date_folder = date.strftime("%Y_%#m_%#d")
#     image_path = os.path.join(image_folder, date_folder)
    
#     if not os.path.exists(image_path):
#         return []

#     image_files = [f for f in os.listdir(image_path) if f.endswith('.jpg')]
#     timestamps = []

#     # Define a regular expression pattern for the timestamp
#     timestamp_pattern = re.compile(r'(\w+)_(\w{3})__(\d{1,2})_(\w{3})_(\d{4})_(\d{1,2})_(\d{1,2})_(\d{1,2}).jpg')

#     for image_file in image_files:
#         match = timestamp_pattern.search(image_file)
#         if match:
#             try:
#                 _, day_of_week, day, month, year, hour, minute, second = match.groups()
#                 timestamp = datetime.strptime(f"{day_of_week} {day} {month} {year} {hour}:{minute}:{second}", '%a %d %b %Y %H:%M:%S')
#                 timestamps.append(timestamp)
#             except ValueError as e:
#                 st.error(f"Error parsing timestamp from filename {image_file}: {e}")
#         else:
#             st.error(f"Filename does not match expected format: {image_file}")
    
#     return sorted(timestamps)

# def image_viewer_section(image_base_path):
#     with st.expander("Image Viewer", expanded=True):
#         # Date input for image viewer
#         image_viewer_date = st.date_input("Select Date for Image Viewer", key="image_viewer_date")
        
#         if st.button("Show Images"):
#             st.session_state['show_images'] = not st.session_state.get('show_images', False)

#         if st.session_state.get('show_images', False):
#             # Get list of timestamps for images
#             timestamps = get_image_timestamps(image_base_path, image_viewer_date)
            
#             if not timestamps:
#                 st.write("No images found for the selected date.")
#                 return

#             # Convert total seconds in a day to 45-second intervals
#             intervals_in_a_day = (24 * 60 * 60) // 45
#             interval_labels = [(datetime(2000, 1, 1) + timedelta(seconds=i * 45)).time().strftime("%H:%M:%S") for i in range(intervals_in_a_day)]
            
#             # Convert min and max timestamps to slider values
#             min_time = timestamps[0]
#             max_time = timestamps[-1]
#             min_slider_val = (min_time.hour * 3600 + min_time.minute * 60 + min_time.second) // 45
#             max_slider_val = (max_time.hour * 3600 + max_time.minute * 60 + max_time.second) // 45
            
#             # Add slider for time range selection
#             start_interval, end_interval = st.select_slider(
#                 "Select Start and End Time",
#                 options=list(range(intervals_in_a_day)),
#                 value=(min_slider_val, max_slider_val),
#                 format_func=lambda x: interval_labels[x],
#                 key="time_range"
#             )

#             # Convert selected intervals to start and end times
#             start_time_seconds = start_interval * 45
#             end_time_seconds = (end_interval + 1) * 45
#             start_time = time((start_time_seconds // 3600) % 24, (start_time_seconds % 3600) // 60, start_time_seconds % 60)
#             end_time = time((end_time_seconds // 3600) % 24, (end_time_seconds % 3600) // 60, end_time_seconds % 60)

#             # Filter images based on selected time range
#             images_to_display = [t for t in timestamps if start_time <= t.time() <= end_time]
            
#             # Show image viewer
#             show_image_viewer(image_base_path, image_viewer_date, images_to_display)

#             # Navigation buttons
#             if images_to_display:
#                 col1, col2, col3 = st.columns([1, 12, 1])
#                 with col1:
#                     if st.button("Previous"):
#                         st.session_state.image_index = (st.session_state.image_index - 1) % len(images_to_display)
#                 with col3:
#                     if st.button("Next"):
#                         st.session_state.image_index = (st.session_state.image_index + 1) % len(images_to_display)