#!/usr/bin/env python3
"""
Test script to verify data loading for <PERSON><PERSON><PERSON> on June 18, 2025
"""

import sys
import os
sys.path.append('Sourcecode')

from monDataLoad import load_data_updated
import json
import pandas as pd
from datetime import datetime

def test_harshil_data():
    print("Testing data loading for <PERSON><PERSON><PERSON> on June 18, 2025...")
    
    # Load configuration
    try:
        with open("resource/Mon_config.json", "r") as f:
            config = json.load(f)
        print("✓ Mon_config.json loaded")
    except Exception as e:
        print(f"✗ Error loading Mon_config.json: {e}")
        return
    
    try:
        with open("resource/UserDataNEW.json", "r") as f:
            user_data = json.load(f)
        print("✓ UserDataNEW.json loaded")
    except Exception as e:
        print(f"✗ Error loading UserDataNEW.json: {e}")
        return
    
    # Find <PERSON><PERSON><PERSON>'s configuration
    harshil_config = None
    for emp_id, emp_data in user_data.items():
        if emp_data.get("EMP_NAME") == "Ha<PERSON><PERSON>":
            harshil_config = emp_data
            print(f"✓ Found <PERSON><PERSON><PERSON> as {emp_id}")
            break
    
    if not harshil_config:
        print("✗ Harshil Chauhan not found in user configuration!")
        return
    
    # Set up parameters for data loading
    csv_directory = config["MainPath"][0]  # "\\\\192.168.1.18\\Archieves\\Mon"
    selected_employee_config = harshil_config
    
    # Load keyword mapping
    try:
        with open("resource/keyword_mapping.json", "r") as f:
            keyword_mapping = json.load(f)
        print("✓ keyword_mapping.json loaded")
    except Exception as e:
        print(f"✗ Error loading keyword_mapping.json: {e}")
        keyword_mapping = {}
    
    # Set date to June 18, 2025
    start_date = datetime(2025, 6, 18)
    end_date = datetime(2025, 6, 18)
    
    print(f"Loading data for date: {start_date.strftime('%Y-%m-%d')}")
    print(f"CSV directory: {csv_directory}")
    print(f"Employee PCs: {harshil_config.get('PCs', [])}")
    
    try:
        # Call the load_data_updated function
        results, combined_log_data, device_logs = load_data_updated(
            csv_directory=csv_directory,
            selected_employee_config=selected_employee_config,
            keyword_mapping=keyword_mapping,
            start_date=start_date,
            end_date=end_date,
            start_time="13:00",
            end_time="05:00"
        )
        
        print(f"✓ Data loading completed successfully!")
        print(f"Results shape: {results.shape}")
        print(f"Combined log data shape: {combined_log_data.shape}")
        print(f"Device logs count: {len(device_logs)}")
        
        if not combined_log_data.empty:
            print(f"Combined log data columns: {list(combined_log_data.columns)}")
            
            # Check for image data
            if 'ImageLocation' in combined_log_data.columns:
                image_count = combined_log_data['ImageLocation'].notna().sum()
                print(f"✓ Images found (ImageLocation): {image_count}")
                if image_count > 0:
                    print("Sample image paths:")
                    sample_images = combined_log_data['ImageLocation'].dropna().head(3)
                    for i, img_path in enumerate(sample_images, 1):
                        print(f"  {i}. {img_path}")
            elif 'pngFile' in combined_log_data.columns:
                image_count = combined_log_data['pngFile'].notna().sum()
                print(f"✓ Images found (pngFile): {image_count}")
                if image_count > 0:
                    print("Sample image paths:")
                    sample_images = combined_log_data['pngFile'].dropna().head(3)
                    for i, img_path in enumerate(sample_images, 1):
                        print(f"  {i}. {img_path}")
            else:
                print("✗ No image columns found in data")
            
            # Check time range
            if 'Time' in combined_log_data.columns:
                time_col = 'Time'
            elif 'LogTime' in combined_log_data.columns:
                time_col = 'LogTime'
            elif 'strTime' in combined_log_data.columns:
                time_col = 'strTime'
            else:
                time_col = None
            
            if time_col:
                combined_log_data[time_col] = pd.to_datetime(combined_log_data[time_col], errors='coerce')
                min_time = combined_log_data[time_col].min()
                max_time = combined_log_data[time_col].max()
                print(f"Time range: {min_time} to {max_time}")
            
        else:
            print("✗ No data found in combined_log_data")
            
    except Exception as e:
        print(f"✗ Error during data loading: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_harshil_data()
