#!/usr/bin/env python3
"""
Debug script to check data availability for <PERSON><PERSON><PERSON> on June 18, 2025
"""

import os
import json
import pandas as pd
from datetime import datetime

def check_harshil_data():
    print("Starting debug script...")

    # Load configuration
    print("Loading configuration files...")
    try:
        with open("resource/Mon_config.json", "r") as f:
            config = json.load(f)
        print("✓ Mon_config.json loaded")
    except Exception as e:
        print(f"✗ Error loading Mon_config.json: {e}")
        return

    try:
        with open("resource/UserDataNEW.json", "r") as f:
            user_data = json.load(f)
        print("✓ UserDataNEW.json loaded")
    except Exception as e:
        print(f"✗ Error loading UserDataNEW.json: {e}")
        return
    
    # Find <PERSON><PERSON><PERSON>'s configuration
    harshil_config = None
    for emp_id, emp_data in user_data.items():
        if emp_data.get("EMP_NAME") == "<PERSON><PERSON><PERSON>":
            harshil_config = emp_data
            print(f"Found <PERSON><PERSON><PERSON> as {emp_id}")
            break
    
    if not harshil_config:
        print("<PERSON><PERSON><PERSON> not found in user configuration!")
        return
    
    print(f"Harshil's PCs: {harshil_config.get('PCs', [])}")
    
    # Check data paths
    main_path = config["MainPath"][0]  # "\\\\192.168.1.18\\Archieves\\Mon"
    print(f"Main data path: {main_path}")
    
    # Check if main path exists
    if not os.path.exists(main_path):
        print(f"ERROR: Main data path does not exist: {main_path}")
        return
    
    # Date for June 18, 2025
    target_date = "2025_6_18"
    print(f"Looking for data on: {target_date}")
    
    # Check each PC configuration
    for pc in harshil_config.get("PCs", []):
        user_name = pc.get("USER_NAME")
        pc_name = pc.get("PC_NAME")
        print(f"\nChecking PC: {pc_name}, User: {user_name}")
        
        # Check both lowercase and uppercase user names
        for username_variant in [user_name.lower(), user_name.upper(), user_name]:
            user_path = os.path.join(main_path, username_variant)
            date_path = os.path.join(user_path, target_date)
            
            print(f"  Checking path: {date_path}")
            
            if os.path.exists(date_path):
                print(f"  ✓ Date folder exists: {date_path}")
                
                # List contents
                contents = os.listdir(date_path)
                print(f"  Contents: {contents}")
                
                # Check for PC folders
                for item in contents:
                    item_path = os.path.join(date_path, item)
                    if os.path.isdir(item_path):
                        print(f"    PC folder: {item}")
                        pc_contents = os.listdir(item_path)
                        print(f"      Contents: {pc_contents}")
                        
                        # Check for log.csv
                        log_file = os.path.join(item_path, "log.csv")
                        if os.path.exists(log_file):
                            print(f"      ✓ log.csv exists")
                            # Check log file size and first few lines
                            try:
                                df = pd.read_csv(log_file, nrows=5)
                                print(f"      Log file columns: {list(df.columns)}")
                                print(f"      Log file shape: {df.shape}")
                                if 'ImageLocation' in df.columns:
                                    image_count = df['ImageLocation'].notna().sum()
                                    print(f"      Images with paths: {image_count}")
                                elif 'pngFile' in df.columns:
                                    image_count = df['pngFile'].notna().sum()
                                    print(f"      Images with paths: {image_count}")
                            except Exception as e:
                                print(f"      Error reading log file: {e}")
                        else:
                            print(f"      ✗ log.csv not found")
                
                # Check for direct log.csv (old format)
                direct_log = os.path.join(date_path, "log.csv")
                if os.path.exists(direct_log):
                    print(f"  ✓ Direct log.csv exists (old format)")
                    try:
                        df = pd.read_csv(direct_log, nrows=5)
                        print(f"    Log file columns: {list(df.columns)}")
                        print(f"    Log file shape: {df.shape}")
                        if 'pngFile' in df.columns:
                            image_count = df['pngFile'].notna().sum()
                            print(f"    Images with paths: {image_count}")
                    except Exception as e:
                        print(f"    Error reading direct log file: {e}")
                        
            else:
                print(f"  ✗ Date folder does not exist: {date_path}")

if __name__ == "__main__":
    check_harshil_data()
