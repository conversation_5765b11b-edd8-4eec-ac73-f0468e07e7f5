import pandas as pd
from collections import defaultdict
import os
import ast
import re
import datetime
import traceback
from datetime import datetime, timedelta
import numpy as np
# This function processes individual rows and applies the keyword mapping
from rapidfuzz import process, fuzz
import monDashboardHelper as monHelper
import numpy as np
from collections import Counter

STR_CONFIG_PATH = r"resource\UserDataNEW.json"
dictUserDataNew = monHelper.ReadJSON(STR_CONFIG_PATH)



# Example usage:
exeMapping = {
    "brave.exe":"Browser",
    "msedge.exe":"Browser",
    "chrome.exe":"Browser",
    "zoho mail - desktop.exe":"Email",
    "explorer.exe":"File Explorer",
    "cursor.exe":"Code Editor",
    "code.exe":"Code Editor",
    "mstsc.exe":"Remote Desktop Connection",
    "taskmgr.exe":"Task Manager",
    "skype.exe":"Skype",
    "clickup.exe":"Clickup"
}
app_dict = {
    'skype': 'Skype',
    'Inbox': 'Mail Inbox',
    'microsoft word': 'Word',
    'google chrome': 'Browser',
    'mozilla fireFox': 'Browser',
    'brave': 'Browser',
    'edge':'Browser',
    'internetexplorer':'Browser'
}
def parse_bd(x):
    if isinstance(x, dict):
        return x
    try:
        return ast.literal_eval(x)
    except Exception:
        return {}
def process_row(row):
    """
    Process each row based on whether it's from an old or new log format.

    Args:
    row (pd.Series): The row of log data.

    Returns:
    tuple: (Updated row, processed status, custom_time_spent)
    """
    # Determine if we're working with the old or new format based on column presence
    if 'appUsed' in row:
        # Old format
        dict_popup = row['dictPopup']
        application = row['appUsed']
        str_time = row['strTime']
    else:
        # New format
        dict_popup = row['BreakDetails']
        application = row['WindowTitle']
        str_time = row['LogTime']

    # Convert dictPopup or BreakDetails from string to dict (if needed)
    try:
        dict_popup = ast.literal_eval(dict_popup)
    except (ValueError, SyntaxError):
        dict_popup = {}

    if not dict_popup:
        return [row], False, None
    
    # Handle multiple BreakTypes and associated meeting/break data
    break_types = dict_popup.get('BreakType', [])
    time_spent_list = dict_popup.get('strTimeInMin', [])
    meeting_with_list = dict_popup.get('Meeting_with', [[]] * len(break_types))
    meeting_topic_list = dict_popup.get('Meeting_Topic', [''] * len(break_types))
    meeting_project_list = dict_popup.get('Meeting_Project', [''] * len(break_types))
    meeting_customer_list = dict_popup.get('Meeting_Customer', [''] * len(break_types))

    # Initialize fields for break or meeting information
    custom_time_spent = [0]*len(break_types)
    processed_rows = []

    for idx, break_type in enumerate(break_types):
        # Extract time spent in minutes for each break/meeting type
        time_spent_min = int(time_spent_list[idx]) if len(time_spent_list) > idx else 0
        custom_time_spent[idx] = time_spent_min * 60  # Time spent in seconds
        
        # custom_time_spent = max(custom_time_spent, time_spent_min * 60)

        # Handle BreakType logic
        if break_type == 'Break':
            processed_row = row.copy()
            
            # Explicitly add missing columns before updating
            processed_row['WindowTitle'] = processed_row.get('WindowTitle', '')
            processed_row['Project'] = processed_row.get('Project', '')
            processed_row['Sub-Project'] = processed_row.get('Sub-Project', '')
            processed_row['Customer'] = processed_row.get('Customer', '')
            processed_row.update({'WindowTitle':'Break', 'Application': 'Break', 'Project': 'Break', 'Sub-Project': 'Break', 'Customer': 'Break'})
            processed_rows.append(processed_row)
        elif break_type == 'Meeting':
            processed_row = row.copy()

            # Clean and extract meeting details
            try:
                meeting_with = ', '.join(meeting_with_list[idx]) if isinstance(meeting_with_list[idx], list) and len(meeting_with_list[idx]) > 1 else (meeting_with_list[idx] if isinstance(meeting_with_list[idx], str) else (', '.join(meeting_with_list[idx]) if isinstance(meeting_with_list[idx], list) and meeting_with_list[idx] else ''))
            except Exception as e:
                meeting_with = meeting_customer_list[idx] if len(meeting_customer_list) > idx else ''

            meeting_topic = meeting_topic_list[idx] if len(meeting_topic_list) > idx else ''
            meeting_project = meeting_project_list[idx] if len(meeting_project_list) > idx else ''
            meeting_customer = meeting_customer_list[idx] if len(meeting_customer_list) > idx else ''

            # Update the row with the meeting information
            processed_row['WindowTitle'] = f'Meeting_{meeting_with}' if meeting_with else application
            processed_row['Application'] = f'Meeting_{meeting_with}' if meeting_with else application
            processed_row['Project'] = meeting_project.capitalize()
            processed_row['Sub-Project'] = meeting_project.capitalize()
            processed_row['Customer'] = meeting_customer.capitalize()
            processed_rows.append(processed_row)
        
        elif break_type == 'DND Customer':
            processed_row = row.copy()

            # pull DND_* lists out of dict_popup
            dnd_with_list     = dict_popup.get('DND_with', [[]]*len(break_types))
            dnd_project_list  = dict_popup.get('DND_Project', ['']*len(break_types))
            dnd_customer_list = dict_popup.get('DND_Customer', ['']*len(break_types))
            dnd_topic_list    = dict_popup.get('DND_Topic', ['']*len(break_types))

            # safely extract the values
            # dnd_with     = (', '.join(dnd_with_list[idx]) 
            #                  if isinstance(dnd_with_list[idx], list) 
            #                  else dnd_with_list[idx])
            dnd_project  = dnd_project_list[idx]
            dnd_customer = dnd_customer_list[idx]
            dnd_topic    = dnd_topic_list[idx]

            # populate the row
            processed_row['WindowTitle'] = f"DND_{dnd_topic}" if dnd_topic else "DND Customer"
            processed_row['Application'] = f'DND Customer_{dnd_topic}' if dnd_topic else application
            processed_row['Project']     = dnd_project.capitalize() or 'DND Customer'
            processed_row['Sub-Project'] = dnd_project.capitalize() or 'DND Customer'
            processed_row['Customer']    = dnd_customer.capitalize() or 'DND Customer'
            processed_rows.append(processed_row)
        elif break_type == 'Half Day Leave':
            processed_row = row.copy()

            # Get Half Day Leave specific fields
            hdl_approved_by_list = dict_popup.get('HalfDayLeave_ApprovedBy', [[]]*len(break_types))
            hdl_notes_list = dict_popup.get('HalfDayLeave_Notes', ['']*len(break_types))

            # Safely extract values
            hdl_approved_by = (', '.join(hdl_approved_by_list[idx]) 
                             if isinstance(hdl_approved_by_list[idx], list) 
                             else hdl_approved_by_list[idx])
            hdl_notes = hdl_notes_list[idx] if len(hdl_notes_list) > idx else ''

            # populate the row
            processed_row['WindowTitle'] = f"Half Day Leave_{hdl_notes}" if hdl_notes else "Half Day Leave"
            processed_row['Application'] = f"Half Day Leave_{hdl_approved_by}_{hdl_notes}"
            processed_row['Project'] = "Half Day Leave"
            processed_row['Sub-Project'] = "Half Day Leave"
            processed_row['Customer'] = "Half Day Leave"
            processed_rows.append(processed_row)


    # If no specific BreakType is found, default to the application
    if not processed_rows:
        row['Application'] = application
        processed_rows.append(row)

    return processed_rows, True, custom_time_spent

# def extract_application(app_used):
#     if not isinstance(app_used, str):
#         return ''
#     possible_applications = re.findall(r'([\w\s]+)(?: - |$)', app_used)
#     return possible_applications[-1].strip() if possible_applications else app_used
def ensure_window_and_exe(df,
                          break_col: str = 'BreakDetails',
                          title_col: str = 'WindowTitle',
                          exe_col: str = 'ExeLocation') -> pd.DataFrame:
    """
    For rows where BreakDetails == '{}', make sure WindowTitle and ExeLocation
    are not empty by forward-filling from the last non-empty value.
    """
    df = df.copy()
    hdr = (df[title_col] == title_col) & (df[exe_col] == exe_col) & (df[break_col] == break_col)
    df = df.loc[~hdr].reset_index(drop=True)
    df = df.dropna(subset=[title_col, exe_col, break_col], how='all')
    
    df[break_col] = df[break_col].replace('', '{}').fillna('{}')
    
    # Normalize empties to NaN
    df[title_col] = df[title_col].replace('', pd.NA)
    df[exe_col]   = df[exe_col].replace('', pd.NA)

    # Boolean mask for the rows we want to fill
    mask = df[break_col] == '{}'

    # Precompute ffilled series
    filled_titles = df[title_col].ffill().bfill()
    filled_exes   = df[exe_col].ffill().bfill()

    # Only fill where BreakDetails is '{}' and the field is missing
    df.loc[mask & df[title_col].isna(), title_col] =  filled_titles[mask & df[title_col].isna()]
    df.loc[mask & df[exe_col].isna(), exe_col] = filled_exes[mask & df[exe_col].isna()]

    return df
def mapApplication(applicationName):
    if isinstance(applicationName, str):
        return exeMapping.get(applicationName.lower(), applicationName)
    else:
        return applicationName
    
def extract_application(app_used, threshold=80):
    """
    Extract the most relevant part of the application name and map it using RapidFuzz.

    Args:
    app_used (str): The raw application name.
    threshold (int): The similarity threshold for mapping.

    Returns:
    str: Mapped or extracted application name.
    """
    # Return empty string if app_used is not a string
    if not isinstance(app_used, str):
        return None
    
    # Extract the most relevant part of the application name
    possible_applications = re.findall(r'([\w\s]+)(?: - |$)', app_used)
    extracted_app = possible_applications[-1].strip() if possible_applications else app_used

    # Check similarity with provided app names
    match = process.extractOne(extracted_app.lower(), app_dict.keys(), scorer=fuzz.ratio)
    
    # Unpack the match details
    if match is not None:
        matched_app, score, _ = match
        
        # If similarity is above the threshold, map it to the provided app name
        if score >= threshold:
            return app_dict[matched_app]
    
    # Otherwise, return the extracted app name as is
    return extracted_app

def get_top_titles_df(log_file, user_name, top_n=50):
    """
    Process a list of log files and return a DataFrame of top N Excel window titles for a known employee.
    """
    title_counts = Counter()

    # for log_file in log_files:
    try:
        df = pd.read_csv(log_file)
        if 'ExeLocation' not in df.columns or 'WindowTitle' not in df.columns:
            print(f"Required columns missing in {log_file}")
            # continue

        excel_df = df[df['ExeLocation'].str.endswith('EXCEL.EXE', na=False)].copy()
        if not excel_df.empty:
            # continue
            # pass
            excel_df['cleaned_title'] = excel_df['WindowTitle'].apply(monHelper.clean_title_Excel)
            title_counts.update(excel_df['cleaned_title'].value_counts().to_dict())
    except Exception as e:
        print(f"Error processing {log_file}: {e}")
    rows = []
    for title, count in title_counts.most_common(top_n):
        application = monHelper.remove_extensions(title) if monHelper.has_extension(title) else title
        # Extract project and customer based on title pattern
        customer = [
            "Drake", "IBMSB", "IKIO", "Royalux", "RV", "DHOA", "Luxeen",
            "Next Practise", "Ekamdeep", "Dr. Sushma", "Steve Bruno"
        ]
        
        Project= [
            "GL", "Purchase Register", "Inventory", "Month Close", "Trended",
            "Payroll", "Lease", "Misc", "BBC", "WIP", "Inventory Valuation Report",
            "Combined GL", "Payroll Register", "Inter Company", "Ramp",
            "Byline Bank Reconciliation", "Wells Fargo Bank Reconciliation",
            "Direct Method Cashflow", "13 Week Cashflow Forecast", "Contribution Margin",
            "Financial Packet", "Financial Workbook", "COGS Supplemental Report",
            "SG&A Supplemental Report", "POWERBI", "Balance sheet Schedule",
            "Closing Workpaper", "Pre-Close", "Sage", "Compliance Certificate", "Audit",
            "Item Costing Report", "AR Aging", "AP Aging", "Bank", "Credit Card", "Loan",
            "AP", "AR", "CC", "Sales Tax", "Commission", "Invoicing"
        ]
        
        customer, project = monHelper.extract_customer_project(title, customer, Project)

        rows.append({
            'Users': user_name,
            'Window_Title': title,
            'Application': application,
            'Project': project,
            'Sub-Project': 'others',
            'Customer': customer,
            'count': count
        })

    return pd.DataFrame(rows)

def get_top_browser_titles_df(log_file,user_name, top_n=50):
    """
    Process a list of log files and return a DataFrame of top N browser window titles for a known employee.
    """
    browser_exes = ('chrome.exe', 'brave.exe', 'firefox.exe')
    title_counts = Counter()

    # for log_file in log_files:
    try:
        df = pd.read_csv(log_file)
        if 'ExeLocation' not in df.columns or 'WindowTitle' not in df.columns:
            print(f"Required columns missing in {log_file}")
            # continue

        # Filter for browser executables
        browser_df = df[df['ExeLocation'].str.endswith(browser_exes, na=False)].copy()
        if not browser_df.empty:
            # continue
            # pass
            # Clean and remove extensions from titles
            browser_df['cleaned_title'] = browser_df['WindowTitle'].apply(monHelper.clean_title_browser)
            title_counts.update(browser_df['cleaned_title'].value_counts().to_dict())

    except Exception as e:
        print(f"Error processing {log_file}: {e}")

    rows = []
    for title, count in title_counts.most_common(top_n):
        rows.append({
            'Users': user_name,
            'Window_Title': title,
            'Application': title,
            'Project': 'others',
            'Sub-Project': 'others',
            'Customer': 'others',
            'count': count
        })

    return pd.DataFrame(rows)

def get_top_CodeEditor_titles_df(log_file,user_name, top_n=10):
    """Generate a DataFrame of top N window titles filtered by exe_name."""
    
    title_counts = Counter()

    # for log_file in log_files:
    try:
        df = pd.read_csv(log_file)
        if 'ExeLocation' not in df.columns or 'WindowTitle' not in df.columns:
            print(f"Required columns missing in {log_file}")
            # continue

            CodeEditor_df = df[df['ExeLocation'].str.endswith('Code.exe', na=False)].copy()
            if not CodeEditor_df.empty:
                # continue
                # paass

                CodeEditor_df['cleaned_title'] = CodeEditor_df['WindowTitle'].apply(monHelper.clean_title_CodeEditor)
                title_counts.update(CodeEditor_df['cleaned_title'].value_counts().to_dict())

    except Exception as e:
        print(f"Error processing {log_file}: {e}")

    rows = []
    for title, count in title_counts.most_common(top_n):
        project_name = monHelper.extract_project_name(title)
        rows.append({
            'Users': user_name,
            'Window_Title': title,
            'Application': project_name if project_name else "CodeEditor",
            'Project': 'others',
            'Sub-Project': 'others',
            'Customer': 'others',
            'count': count
        })

    return pd.DataFrame(rows)

def remap_window_title(log_df, mapping_df,strUser):
    """
    Remaps Application, Project, and Customer in log_df based on partial matches 
    between log_df['WindowTitle'] and mapping_df['Window_Title'], retaining all columns.
    
    Args:
        log_df (pd.DataFrame): DataFrame from Ankita17.csv with log data
        mapping_df (pd.DataFrame): DataFrame from Account.csv with mapping rules
    
    Returns:
        pd.DataFrame: Updated DataFrame with all original columns and remapped fields
    """
    # Strip spaces from Users column and filter for the specific user
    mapping_df['Users'] = mapping_df['Users'].str.strip()
    try:
        mapping_df = mapping_df[mapping_df['Users'] == strUser].copy()
    except Exception as e:
        mapping_df = pd.DataFrame()
    
    # Create a copy of the input DataFrame to avoid modifying the original
    result_df = log_df.copy()
    result_df["IsRemapped"] = False
    # Process each row in log_df
    for idx, row in result_df.iterrows():
        title_clean = monHelper.clean_title(row['WindowTitle'])
        if not title_clean:
            continue  # Skip if cleaned title is empty
        
        # Iterate through mapping_df to find matches
        for _, mapping_row in mapping_df.iterrows():
            mapping_title_clean = monHelper.clean_title(mapping_row['Window_Title'])
            if not mapping_title_clean:
                continue  # Skip if mapping title is empty after cleaning
            
            # Check if mapping_title_clean is a substring of title_clean
            if mapping_title_clean in title_clean:
                # Assign Application, Project, and Customer if not null in mapping
                if pd.notnull(mapping_row['Application']):
                    result_df.at[idx, 'Application'] = f"{row['Application']}_{mapping_row['Application']}"
                    result_df.at[idx, 'IsRemapped']  = True         
                if pd.notnull(mapping_row['Project']):
                    result_df.at[idx, 'Project'] = mapping_row['Project']
                if pd.notnull(mapping_row['Customer']):
                    result_df.at[idx, 'Customer'] = mapping_row['Customer']
                break  # Stop after first match to avoid multiple updates

    # Return the entire DataFrame with all columns
    return result_df

def convert_to_network_path(image_path, log_dir):
    """
    Convert local image paths to network paths based on the log directory.
    """
    # If the image path is already a network path, return as is
    if image_path.startswith('\\\\'):
        return image_path

    # If the log_dir is a network path, construct the network path for the image
    if log_dir.startswith('\\\\'):
        # Check if it's a local path that needs conversion
        if len(image_path) > 1 and image_path[1] == ':':  # Local path like D:\...
            # Extract the relative path from the local path
            # Convert D:\mon\Data\harshil\2025_6_18\DEV\filename.jpg
            # to \\************\Archieves\Mon\harshil\2025_6_18\DEV\filename.jpg

            # Split the local path to extract the relevant parts
            path_parts = image_path.replace('\\', '/').split('/')

            # Find the user folder and date folder in the path
            user_folder = None
            date_folder = None
            pc_folder = None
            filename = os.path.basename(image_path)

            for i, part in enumerate(path_parts):
                # Look for date pattern (YYYY_M_DD)
                if '_' in part and len(part.split('_')) == 3:
                    try:
                        year, _, _ = part.split('_')  # month and day not used
                        if len(year) == 4 and year.isdigit():
                            date_folder = part
                            if i > 0:
                                user_folder = path_parts[i-1]
                            if i < len(path_parts) - 2:
                                pc_folder = path_parts[i+1]
                            break
                    except:
                        continue

            if user_folder and date_folder and pc_folder:
                # Construct network path: \\server\share\user\date\pc\filename
                network_base = log_dir.rsplit('\\', 2)[0]  # Remove PC and date folders from log_dir
                network_path = os.path.join(network_base, user_folder, date_folder, pc_folder, filename)
                return network_path.replace('/', '\\')

        # Extract just the filename from the image path
        filename = os.path.basename(image_path)
        # Combine with the network log directory
        return os.path.join(log_dir, filename).replace('/', '\\')

    # If both are local paths, return the original image path
    return image_path

def populate_image_locations(log_data, log_filepath):
    """
    Populate empty ImageLocation fields with actual image files based on timestamps.
    """
    import glob
    from datetime import datetime, timedelta

    # Get the directory containing the log file
    log_dir = os.path.dirname(log_filepath)

    # Find all image files in the directory
    image_patterns = ['*.jpg', '*.jpeg', '*.png', '*.bmp']
    image_files = []
    for pattern in image_patterns:
        image_files.extend(glob.glob(os.path.join(log_dir, pattern)))

    if not image_files:
        return log_data  # No image files found

    # Create a mapping of timestamps to image files
    image_timestamp_map = {}
    for img_file in image_files:
        try:
            # Extract timestamp from filename (assuming format like: harshil_Wed__18_Jun_2025_00_01_52.jpg)
            filename = os.path.basename(img_file)
            # Try to extract timestamp from filename
            # Format: harshil_Wed__18_Jun_2025_00_01_52.jpg
            parts = filename.split('_')
            if len(parts) >= 7:
                # Extract date and time parts
                # day_part = parts[2]  # Wed (not used)
                date_part = parts[3]  # 18
                month_part = parts[4]  # Jun
                year_part = parts[5]  # 2025
                hour_part = parts[6]  # 00
                min_part = parts[7]  # 01
                sec_part = parts[8].split('.')[0]  # 52 (remove extension)

                # Create datetime string
                datetime_str = f"{date_part} {month_part} {year_part} {hour_part}:{min_part}:{sec_part}"
                img_timestamp = datetime.strptime(datetime_str, "%d %b %Y %H:%M:%S")
                image_timestamp_map[img_timestamp] = img_file
        except Exception:
            # If timestamp extraction fails, skip this image
            continue

    if not image_timestamp_map:
        return log_data  # No valid timestamps found in image files

    # Convert LogTime to datetime for comparison
    log_data['LogTime_dt'] = pd.to_datetime(log_data['LogTime'], format='%a, %d %b %Y %H:%M:%S', errors='coerce')

    # First, convert any existing local paths to network paths
    for idx, row in log_data.iterrows():
        if pd.notna(row['ImageLocation']) and row['ImageLocation'] != '':
            current_path = row['ImageLocation']
            # If it's a local path (starts with drive letter), convert to network path
            if len(current_path) > 1 and current_path[1] == ':':  # Local path like D:\...
                network_path = convert_to_network_path(current_path, log_dir)
                log_data.at[idx, 'ImageLocation'] = network_path

    # Then, for each log entry with empty ImageLocation, find the closest image file
    for idx, row in log_data.iterrows():
        if pd.isna(row['ImageLocation']) or row['ImageLocation'] == '':
            log_time = row['LogTime_dt']
            if pd.notna(log_time):
                # Find the closest image timestamp (within 2 minutes)
                closest_img = None
                min_diff = timedelta(minutes=2)  # Maximum allowed difference

                for img_time, img_path in image_timestamp_map.items():
                    time_diff = abs(log_time - img_time)
                    if time_diff < min_diff:
                        min_diff = time_diff
                        closest_img = img_path

                if closest_img:
                    # Convert local path to network path if needed
                    network_path = convert_to_network_path(closest_img, log_dir)
                    log_data.at[idx, 'ImageLocation'] = network_path

    # Drop the temporary datetime column
    log_data = log_data.drop('LogTime_dt', axis=1)

    return log_data

def process_log_file(log_filepath, keyword_mapping, total_time, start_time, end_time, time_spans_two_days=False):
    # Ensure start_time and end_time are in datetime.time format
    if isinstance(start_time, str):
        start_time = datetime.strptime(start_time, "%H:%M").time()
    if isinstance(end_time, str):
        end_time = datetime.strptime(end_time, "%H:%M").time()
        
    # Read the first line of the CSV to analyze the format
    with open(log_filepath, 'r',errors='ignore') as f:
        first_line = f.readline().split(',')
    
    
    # Check if the first column contains a numeric value (old format) or a PC name (new format)
    try:
        n_cols = len(first_line)
        if first_line[0].strip().isdigit() or first_line[0].strip() == '':
            # Old format (first column is an index or empty)
            headers = ['', 'appUsed', 'dictPopup', 'indx', 'pngFile', 'strTime']
            old_format = True
        elif n_cols >= 6:
            # New format (first column contains PCName)
            headers = ['PCName', 'WindowTitle', 'ExeLocation', 'BreakDetails', 'ImageLocation', 'LogTime','ISDNDActive', 'Extracted_URL']
            old_format = False
        else:
            raise ValueError(
                f"Unrecognized log-file format: expected 6 cols (old) or ≥8 cols (new), got {n_cols}")
    except Exception as e:
        raise ValueError(f"Unable to determine log file format: {str(e)}")

    # Read the log data with the appropriate headers
    log_data = pd.read_csv(log_filepath, names=headers,encoding = 'cp850')
    log_data = ensure_window_and_exe(log_data)

    # Populate empty ImageLocation fields with actual image files if they exist
    if not old_format and 'ImageLocation' in log_data.columns:
        log_data = populate_image_locations(log_data, log_filepath)
    # Process old or new columns accordingly
    if old_format:
        # Old format column processing
        log_data['Device Name'] = "Unknown Device"
        log_data['WindowTitle'] = log_data['appUsed']
        log_data['Application'] = log_data['appUsed']
        log_data['Extracted_URL'] = ""
        
        log_data['Application'] = log_data['Application'].apply(extract_application)
        # log_data['Time'] = pd.to_datetime(log_data['strTime'], errors='coerce')
        log_data['Time'] = pd.to_datetime(log_data['strTime'], errors='coerce')

        log_data['ImageLocation'] = log_data['pngFile']  # Rename old pngFile to ImageLocation for consistency
        # Convert local paths to network paths for old format
        log_dir = os.path.dirname(log_filepath)
        log_data['ImageLocation'] = log_data['ImageLocation'].apply(
            lambda x: convert_to_network_path(x, log_dir) if pd.notna(x) and x != '' else x
        )
    else:
        # New format column processing
        log_data['Device Name'] = log_data['PCName']
        log_data['WindowTitle'] = log_data['WindowTitle']
        # Extract only the exe name from the full path in ExeLocation
        log_data['Application'] = log_data['ExeLocation'].apply(lambda x: os.path.basename(x) if isinstance(x, str) else x)
        log_data['Application'] = log_data['Application'].apply(mapApplication)

        log_data['Time'] = pd.to_datetime(log_data['LogTime'], format='%a, %d %b %Y %H:%M:%S', errors='coerce')

    # log_data['Application'] = log_data['Application'].fillna('Desktop')
    # log_data['WindowTitle'] = log_data['WindowTitle'].fillna('Desktop')
    
    if 'ISDNDActive' in log_data.columns:
        log_data = log_data[~((log_data['ISDNDActive'] == True) & (log_data['BreakDetails'] != '{}'))]
        
    # Sort by 'Time' to ensure time sequence is correct
    log_data = log_data.sort_values(by='Time')

    # Adjust log filtering based on start and end time across two days
    if start_time < end_time:
        # Standard case: start and end time are on the same day
        mask = (log_data['Time'].dt.time >= start_time) & (log_data['Time'].dt.time <= end_time)
    else:
        # Work time spans over two days (e.g., 1 PM to 8 AM)
        mask = (log_data['Time'].dt.time >= start_time) | (log_data['Time'].dt.time <= end_time)

    # Apply the mask to filter the log data
    log_data = log_data[mask]

    # Calculate time spent between log entries (using consecutive differences)
    # log_data['TimeSpent'] = log_data['Time'].diff().dt.total_seconds().fillna(0)

    # Capping time diff at 10 min.
    # Step 1: Compute the actual time difference
    log_data['TimeSpentRaw'] = log_data['Time'].diff().dt.total_seconds().fillna(0)

    # Step 2: Apply the condition based on BreakDetails
    log_data['TimeSpent'] = log_data.apply(
        lambda row: min(row['TimeSpentRaw'], 600) if row['BreakDetails'] == '{}' else row['TimeSpentRaw'],
        axis=1
    )

    # Set a maximum time difference threshold (e.g., 5 hours) to ignore large gaps
    MAX_TIME_DIFF_IN_SEC = 5 * 3600
    log_data['TimeSpent'] = log_data['TimeSpent'].apply(lambda x: x if 0 < x <= MAX_TIME_DIFF_IN_SEC else 0)
    # Add application data and additional fields for later processing
    # log_data['strDate'] = log_data['strTime'].dt.strftime('%a, %d %b %Y')
    log_data['Project'] = ''
    log_data['Sub-Project'] = ''
    log_data['Customer'] = ''

    # Map the application names and apply keyword mapping
    results = []
    for _, row in log_data.iterrows():
        # Process each row, which may return multiple processed rows
        processed_rows, processed, custom_time_spent = process_row(row)
        
        # Iterate through all processed rows (multiple entries for break/meeting)
        for idx, updated_row in enumerate(processed_rows):
        
            if processed:
                # Skip added breaks and meetings that are more than default thereshold 
                if  custom_time_spent[idx] > MAX_TIME_DIFF_IN_SEC:
                    continue
                
            else:
                app_used_lower = str(updated_row['Application']).lower()
                mapping_found = False  # Flag to track if mapping was found

                for keyword, mappings in keyword_mapping.items():
                    keywords = ast.literal_eval(keyword) if keyword.startswith('(') and keyword.endswith(')') else [keyword]
                    if any(kw.lower() in app_used_lower for kw in keywords):
                        for mapping in mappings:
                            updated_row['Project'] = mapping.get('Project', '')
                            updated_row['Sub-Project'] = mapping.get('Sub-Project', '')
                            updated_row['Customer'] = mapping.get('Customer', '')
                        mapping_found = True  # Mapping was found
                        break

                # If no mapping was found, assign "others"
                if not mapping_found:
                    updated_row['Project'] = updated_row.get('Project', 'others') or 'others'
                    updated_row['Sub-Project'] = updated_row.get('Sub-Project', 'others') or 'others'
                    updated_row['Customer'] = updated_row.get('Customer', 'others') or 'others'
            
            # Update the total time per key (project, sub-project, customer, etc.)
            shifted_datetime = updated_row['Time']  # By default, no change
            if time_spans_two_days:
                if shifted_datetime.time() <= end_time:
                    shifted_datetime = shifted_datetime - pd.Timedelta(days=1)
            
            key = (
                updated_row['Device Name'],
                updated_row['WindowTitle'],
                updated_row['Application'],
                updated_row['Project'],
                updated_row['Sub-Project'],
                updated_row['Customer'],
                shifted_datetime.strftime('%Y-%m-%d')
            )
          
            total_time[key] += custom_time_spent[idx] if custom_time_spent else updated_row['TimeSpent']

            # Append the updated row to the results list
            results.append(updated_row)

    # Return the processed log data
    return pd.DataFrame(results)

# def merge_logs_based_on_rdp_old(combined_log_data, device_logs):
#     """ 
#     Merge logs from different devices and avoid double counting time if RDP is used.
#     """

#     # Step 1: Identify Main Devices Using RDP
#     rdp_devices = {}  

#     for device_name, log_data in device_logs.items():
#         #  Check if RDP was used on this device
#         rdp_log = log_data[log_data['Application'].str.contains(r"mstsc.exe|rdpclip.exe|remotedesktop.exe", case=False, na=False)]

#         if not rdp_log.empty:
#             # Store the RDP connection timestamps
#             rdp_devices[device_name] = rdp_log[['Device Name', 'Time']]

#     #  Step 2: Compare Main Device & Remote Device Logs
#     for main_device, rdp_times in rdp_devices.items():
#         for remote_device, remote_log in device_logs.items():
#             if main_device == remote_device:
#                 continue  # Skip same device

#             for _, rdp_row in rdp_times.iterrows():
#                 rdp_start = rdp_row['Time']

#                 #  Check if remote device was active at the same time
#                 overlapping_logs = remote_log[
#                     (remote_log['Time'] >= rdp_start) & 
#                     (remote_log['Time'] <= rdp_start + pd.Timedelta(minutes=30))  # Assuming RDP session lasts max 30 min
#                 ]

#                 if not overlapping_logs.empty:
#                     #Overlap found, so remove duplicate time from main device
#                     combined_log_data.loc[
#                         (combined_log_data['Device Name'] == main_device) & 
#                         (combined_log_data['Time'] >= rdp_start) & 
#                         (combined_log_data['Time'] <= rdp_start + pd.Timedelta(minutes=30)),
#                         'TimeSpent'
#                     ] = 0  # Set time spent to 0 in main device

#     return combined_log_data

# def merge_logs_based_on_rdp_old_2(combined_log_data, device_logs, selected_employee_config):
    
#     # Create a copy and remove any extra spaces from column names.
#     df = combined_log_data.copy()
#     df.columns = df.columns.str.strip()
    
#     # Convert LogTime to datetime objects. This will now work since the column name is clean.
#     df['LogTime'] = pd.to_datetime(df['LogTime'])
    
#     # -----------------------------
#     # 1. Split logs by device type
#     # -----------------------------
#     # Define devices that represent remote connections (RDP devices)
#     # rdp_devices = ["MAIN", "RIVER-DEV3"]
#     rdp_devices = []
#     main_device = ""
#     for dictPC in selected_employee_config["PCs"]:
#         if "isMain" in dictPC.keys():
#             if dictPC["isMain"] == True:
#                 print("Skipping Main PC: ", dictPC["PC_NAME"])
#                 main_device = dictPC["PC_NAME"]
#         else:
#             print(dictPC["PC_NAME"])
#             rdp_devices.append(dictPC["PC_NAME"])
    
#     # Logs from remote PCs (MAIN and RIVER-DEV3)
#     df_other = df[df['PCName'].isin(rdp_devices)].copy()
    
#     # Logs from RIVER-DEV11 (the candidate rows to potentially drop)
#     # df_logs_main_pc = df[df['PCName'] == "RIVER-DEV11"].copy()

#     # Logs from Main (the candidate rows to potentially drop)
#     df_logs_main_pc = df[df['PCName'] == main_device].copy()
    
#     # -----------------------------
#     # 2. Identify RDP active rows in RIVER-DEV11
#     # -----------------------------
#     # Only consider dropping rows where ExeLocation indicates an RDP session.
#     # This regex checks for "mstsc.exe", "rdpclip.exe", or "remotedesktop.exe" (case-insensitive)
#     rdp_exe_pattern = r'mstsc\.exe|rdpclip\.exe|remotedesktop\.exe'
#     rdp_active_mask = df_logs_main_pc['ExeLocation'].str.contains(rdp_exe_pattern, case=False, na=False)
    
#     # Partition RIVER-DEV11 logs into:
#     # - RDP active rows (candidates for dropping)
#     # - Non-RDP rows (always kept)
#     df_river_rdp = df_logs_main_pc[rdp_active_mask].copy()
#     df_river_nonrdp = df_logs_main_pc[~rdp_active_mask].copy()
    
#     # -----------------------------
#     # 3. Check for concurrent usage with other PCs
#     # -----------------------------
#     # For each RDP active RIVER-DEV11 row, we check if there is a log from MAIN or RIVER-DEV3
#     # within a 10-minute window.
#     # Sort by LogTime for the merge_asof operation.
#     df_river_rdp_sorted = df_river_rdp.sort_values('LogTime')
#     df_other_sorted = df_other.sort_values('LogTime')
    
#     # Prepare the "other PC" logs for merging (rename LogTime to avoid overwriting)
#     df_other_merge = df_other_sorted[['LogTime']].rename(columns={'LogTime': 'Other_LogTime'})
    
#     # Use merge_asof to join each RDP active RIVER-DEV11 row with the nearest other PC log
#     merged_rdp = pd.merge_asof(
#         df_river_rdp_sorted,
#         df_other_merge,
#         left_on='LogTime',
#         right_on='Other_LogTime',
#         direction='nearest'
#     )
    
#     # Calculate the absolute time difference between the RIVER-DEV11 row and its nearest other PC log
#     merged_rdp['time_diff'] = (merged_rdp['LogTime'] - merged_rdp['Other_LogTime']).abs()
    
#     # Define a 10-minute threshold
#     time_threshold = pd.Timedelta(minutes=10)
    
#     # Mark rows to drop if:
#     # - They are RDP active (ExeLocation match) in RIVER-DEV11, and
#     # - There is a log from another PC (MAIN or RIVER-DEV3) within 10 minutes.
#     drop_mask = (~merged_rdp['Other_LogTime'].isna()) & (merged_rdp['time_diff'] <= time_threshold)
    
#     # Prepare a DataFrame of dropped rows (for return/debugging)
#     dropped_df = merged_rdp[drop_mask].copy()
    
#     # The kept RDP rows are those that do NOT satisfy the drop condition.
#     kept_rdp_df = merged_rdp[~drop_mask].copy()
    
#     # -----------------------------
#     # 4. Build Final DataFrame
#     # -----------------------------
#     # For RIVER-DEV11 logs, keep:
#     # - All non-RDP rows
#     # - RDP active rows that were not dropped
#     final_river_df = pd.concat([df_river_nonrdp, kept_rdp_df[df_logs_main_pc.columns]], ignore_index=True)
    
#     # Combine final RIVER-DEV11 logs with all logs from remote PCs (MAIN and RIVER-DEV3)
#     final_df = pd.concat([df_other, final_river_df], ignore_index=True).sort_values('LogTime')
    
#     # -----------------------------
#     # (Optional) Debug Information:
#     # -----------------------------
#     print("Unique PCName values:", df['PCName'].unique())
#     print("Counts per PCName:")
#     print(df['PCName'].value_counts())
#     print(f"Total remote PC (MAIN and RIVER-DEV3) rows: {len(df_other)}")
#     print(f"Total RIVER-DEV11 rows: {len(df_logs_main_pc)}")
#     print(f"RIVER-DEV11 RDP active rows (candidates for drop): {len(df_river_rdp)}")
#     print("RIVER-DEV11 RDP active rows to DROP (within 10 minutes of an other PC log):")
#     print(dropped_df)
#     print("RIVER-DEV11 RDP active rows to KEEP (no nearby other PC log within 10 minutes):")
#     print(kept_rdp_df)
    
#     # Return both the final DataFrame and the dropped rows DataFrame.
#     return final_df, dropped_df

def merge_logs_based_on_rdp(combined_log_data, device_logs, selected_employee_config,nThreshold=180):
    """
    Process the combined_log_data DataFrame:
      - Converts the 'RealTime' column to datetime.
      - Sorts the DataFrame by 'RealTime' in ascending (old to new) order.
      - Computes the time difference between consecutive rows in seconds.
      - Applies a custom calculation to generate a new column based on 'BreakDetails':
          * If 'BreakDetails' is "{}", the new value is the minimum of the time difference and 600 seconds.
          * Otherwise, the new value is the full time difference.

    Parameters:
    -----------
    combined_log_data : pd.DataFrame
        Input DataFrame containing at least 'RealTime' and 'BreakDetails' columns.
    device_logs : Any
        Ignored parameter.
    selected_employee_config : Any
        Ignored parameter.

    Returns:
    --------
    final_df : pd.DataFrame
        The processed DataFrame with the additional columns.
    """

    # Copy the input DataFrame to avoid modifying the original data.
    df = combined_log_data.copy()

    # Convert 'RealTime' to datetime (adjust the format if needed)
    if 'RealTime' not in df.columns:
        df['RealTime'] = pd.NaT
    df['RealTime'] = pd.to_datetime(df['RealTime'], format='%d-%b-%Y %H:%M', errors='coerce')

    # Sort the DataFrame by 'RealTime' (old to new order)
    df = df.sort_values('RealTime').reset_index(drop=True)

    # Compute the time difference in seconds between consecutive rows.
    # The first row does not have a previous row so its diff will be NaN; we fill it with 0.
    df['TimeDiffSec'] = df['RealTime'].diff().dt.total_seconds().fillna(0)
    
    if 'BreakDetails' not in df.columns:
        df['BreakDetails'] = '{}'
    df['BreakDetails'] = df['BreakDetails'].fillna('{}')
    df['BreakDetailsDict'] = df['BreakDetails'].apply(parse_bd)
    empty_mask = df['BreakDetailsDict'].apply(lambda d: not bool(d))
    
    df['TimeSpentActual'] = np.where(empty_mask,np.minimum(df['TimeDiffSec'], 600),df['TimeDiffSec'])
    short_break_mask = (df['TimeDiffSec'] > nThreshold) & (df['TimeDiffSec'] < 600) & empty_mask
    df.loc[short_break_mask, 'Application'] = 'Short Break'
    for i in range(len(df) - 1):
        bd1 = df.at[i, 'BreakDetailsDict']
        bd2 = df.at[i + 1, 'BreakDetailsDict']
        # identical break-details dict and second row time-diff is zero
        if bd1 == bd2 and df.at[i + 1, 'TimeDiffSec'] == 0:
            times = bd1.get('strTimeInMin', [])
            # ensure we have at least two entries
            if len(times) >= 2 and times[0].isdigit() and times[1].isdigit():
                df.at[i,     'TimeSpentActual'] = int(times[0]) * 60
                df.at[i + 1, 'TimeSpentActual'] = int(times[1]) * 60
        elif 'BreakType' in bd1 and 'Half Day Leave' in bd1.get('BreakType', []):
            idx = bd1.get('BreakType', []).index('Half Day Leave') if 'Half Day Leave' in bd1.get('BreakType', []) else -1
            if idx >= 0 and 'strTimeInMin' in bd1 and len(bd1['strTimeInMin']) > idx and bd1['strTimeInMin'][idx].isdigit():
                df.at[i, 'TimeSpentActual'] = int(bd1['strTimeInMin'][idx]) * 60
    # # Define a helper function to implement the custom calculation.
    # def custom_time_calc(row):
    #     if row['BreakDetails'] == '{}':
    #         return min(row['TimeDiffSec'], 600)  # Cap the value at 600 seconds if BreakDetails is "{}"
    #     else:
    #         return row['TimeDiffSec']

    # # Apply the helper function row-wise to generate the new column.
    # df['TimeSpentActual'] = df.apply(custom_time_calc, axis=1)

    # Return the final DataFrame
    return df
 
# def separate_logs_by_device(device_logs):
    """ 
    Separate logs for each device while accounting for RDP sessions to avoid double counting.
    """

    # Step 1: Identify Main Devices Using RDP
    rdp_devices = {}

    for device_name, log_data in device_logs.items():
        # Check if RDP was used on this device
        rdp_log = log_data[log_data['Application'].str.contains(r"mstsc.exe|rdpclip.exe|remotedesktop.exe", case=False, na=False)]

        if not rdp_log.empty:
            # Store the RDP connection timestamps
            rdp_devices[device_name] = rdp_log[['Device Name', 'Time']]

    # Step 2: Create a dictionary to store separate device-wise logs
    device_wise_logs = {device: log_data.copy() for device, log_data in device_logs.items()}

    # Step 3: Adjust the logs for devices where RDP was detected
    for main_device, rdp_times in rdp_devices.items():
        for remote_device, remote_log in device_logs.items():
            if main_device == remote_device:
                continue  # Skip same device

            for _, rdp_row in rdp_times.iterrows():
                rdp_start = rdp_row['Time']

                # Check if remote device was active at the same time
                overlapping_logs = remote_log[
                    (remote_log['Time'] >= rdp_start) & 
                    (remote_log['Time'] <= rdp_start + pd.Timedelta(minutes=30))  # Assuming RDP session lasts max 30 min
                ]

                if not overlapping_logs.empty:
                    # Overlap found, so remove duplicate time from main device
                    device_wise_logs[main_device].loc[
                        (device_wise_logs[main_device]['Time'] >= rdp_start) & 
                        (device_wise_logs[main_device]['Time'] <= rdp_start + pd.Timedelta(minutes=30)),
                        'TimeSpent'
                    ] = 0  # Set time spent to 0 in main device

    return device_wise_logs  # Returns a dictionary with logs per device

# def separate_logs_by_device(combined_log_data):
    """
    Separates logs for each device from the combined log data.
    """
    device_wise_logs = {}

    if isinstance(combined_log_data, pd.DataFrame):
        # Ensure 'Device Name' column exists
        if 'Device Name' not in combined_log_data.columns:
            raise ValueError("Missing 'Device Name' column in log data")

        # Group logs by device name
        for device, log_data in combined_log_data.groupby('PCName'):
            device_wise_logs[device] = log_data.copy()  # Store each device's logs separately

    else:
        # If logs are in list-of-dict format
        for log in combined_log_data:
            device_id = log.get("PCName")  # Ensure key exists
            if device_id not in device_wise_logs:
                device_wise_logs[device_id] = []
            device_wise_logs[device_id].append(log)

    return device_wise_logs


# def load_data(csv_directory, keyword_mapping, start_date=None, end_date=None, start_time="13:00", end_time="05:00"):
#     total_time = defaultdict(float)
#     combined_log_data = pd.DataFrame()  # To combine logs from multiple days
#     image_data = []  # To store image data (ImageLocation information)

#     # Convert start_time and end_time to datetime.time objects
#     try:
#         start_time = datetime.strptime(start_time, "%H:%M").time()
#         end_time = datetime.strptime(end_time, "%H:%M").time()
#     except ValueError as e:
#         raise ValueError("Invalid time format for start_time or end_time. Please use 'HH:MM' format.") from e

#     # Check if the time range spans across two days
#     time_spans_two_days = start_time > end_time
    
#      # Store logs per device
#     device_logs = {}

#     # Create a date range if start_date and end_date are provided
#     date_range = []
#     if start_date and end_date:
#         try:
#             date_range = pd.date_range(start=start_date, end=end_date).to_pydatetime()
#         except Exception as e:
#             raise ValueError("Invalid date format for start_date or end_date. Please use 'YYYY-MM-DD'.") from e
#     else:
#         date_range = []  # No date filtering

#     # Process each date folder within the date range
#     for current_date in date_range:
#         folder_name = f"{current_date.year}_{current_date.month}_{current_date.day}"
#         folder_path = os.path.join(csv_directory, folder_name)

#         if not os.path.exists(folder_path):
#             continue  # Skip non-existent folders

#         # Process logs based on folder structure (new or old format)
        
#         # Old format, log and images are directly in the date folder
#         log_file_path = os.path.join(folder_path, 'log.csv')
#         if os.path.exists(log_file_path):
#             try:
#                 day_log_data = process_log_file(log_file_path, keyword_mapping, total_time, start_time, "23:59")
#                 mapping_df = pd.read_csv(r"resource\Account.csv")
#                 day_log_data = remap_window_title(day_log_data,mapping_df,"Ankita Kotak")
#                 day_log_data['Device Name'] = "Unknown Device"
#                 combined_log_data = pd.concat([combined_log_data, day_log_data], ignore_index=True)

#                 # If the time range spans two days, process the next day as well
#                 if time_spans_two_days:
#                     next_day = current_date + timedelta(days=1)
#                     next_day_fol_name = f"{next_day.year}_{next_day.month}_{next_day.day}"
                    
#                     # Old Formatted name
#                     next_day_folder = os.path.join(csv_directory, next_day_fol_name)
#                     next_day_log_file = os.path.join(next_day_folder, 'log.csv')

#                     if os.path.exists(next_day_log_file):
#                         next_day_log_data = process_log_file(next_day_log_file, keyword_mapping, total_time, "00:00", end_time, time_spans_two_days=True)
#                         mapping_df = pd.read_csv(r"resource\Account.csv")
#                         next_day_log_data = remap_window_title(next_day_log_data,mapping_df,"Ankita Kotak")
#                         next_day_log_data['Device Name'] = "Unknown Device"
#                         combined_log_data = pd.concat([combined_log_data, next_day_log_data], ignore_index=True)
#                     else :
#                         for device_name in os.listdir(os.path.join(csv_directory, next_day_fol_name)):
#                             device_path = os.path.join(folder_path, device_name)
#                             if os.path.isdir(device_path):  # New format with device folder
#                                 next_day_log_file = os.path.join(device_path, 'log.csv')
#                                 if os.path.exists(next_day_log_file):
#                                     next_day_log_data = process_log_file(next_day_log_file, keyword_mapping, total_time, "00:00", end_time)
#                                     mapping_df = pd.read_csv(r"resource\Account.csv")
#                                     next_day_log_data = remap_window_title(next_day_log_data,mapping_df,"Ankita Kotak")
#                                     next_day_log_data['Device Name'] = device_name
#                                     combined_log_data = pd.concat([combined_log_data, next_day_log_data], ignore_index=True)
                                    
#             except Exception as e:
#                 print(f"Error processing log file for Unknown Device on {current_date}: {e}")
#         else:
#             # New format, PC(Device) Name inside date folder
#             for device_name in os.listdir(folder_path):
#                 device_path = os.path.join(folder_path, device_name)
#                 if os.path.isdir(device_path):  # New format with device folder
#                     log_file_path = os.path.join(device_path, 'log.csv')
#                     try:
#                         # Process main log file for the day
#                         if os.path.exists(log_file_path):
#                             day_log_data = process_log_file(log_file_path, keyword_mapping, total_time, start_time, "23:59")
#                             # day_log_data['Device Name'] = device_name
#                             # device_logs[device_name] = day_log_data 
#                             # combined_log_data = pd.concat([combined_log_data, day_log_data], ignore_index=True)

#                             if not day_log_data.empty:  # Ensure there is data before adding
#                                     day_log_data['Device Name'] = device_name
#                                     device_logs[device_name] = day_log_data 
#                                     combined_log_data = pd.concat([combined_log_data, day_log_data], ignore_index=True) 

#                                     if combined_log_data.empty:  
#                                         combined_log_data = day_log_data.copy()  # Initialize directly if empty
                              


#                             # If the time range spans two days, process the next day as well
#                             if time_spans_two_days:
#                                 next_day = current_date + timedelta(days=1)
#                                 next_day_fol_name = f"{next_day.year}_{next_day.month}_{next_day.day}"
#                                 next_day_folder = os.path.join(csv_directory, next_day_fol_name, device_name)
#                                 next_day_log_file = os.path.join(next_day_folder, 'log.csv')

#                                 if os.path.exists(next_day_log_file):
#                                     next_day_log_data = process_log_file(next_day_log_file, keyword_mapping, total_time, "00:00", end_time, time_spans_two_days=True)
#                                     if not next_day_log_data.empty:
#                                         next_day_log_data['Device Name'] = device_name
#                                         # Subtract 1 day from all timestamps
#                                         next_day_log_data['Time'] = next_day_log_data['Time'] - pd.Timedelta(days=1)
#                                         combined_log_data = pd.concat([combined_log_data, next_day_log_data], ignore_index=True)
#                                     else:
#                                         # If next_day_log_data is empty, keep combined_log_data unchanged
#                                         combined_log_data = combined_log_data    

#                     except Exception as e:
#                         print(f"Error processing log file for {device_name} on {current_date}: {e}")
#                         # Load filtering rules from JSON
#                         #color_palette = monHelper.ReadJSON("Sourcecode/color_palette.json")
#                         # Load filtering rules from JSON within the else block
#     try:
#         # with open("resource/logdrop.json", "r") as file:
#             filter_config = monHelper.ReadJSON("resource/logdrop.json")
#     except FileNotFoundError:
#         print("Filter configuration file 'logdrop.json' not found.")
#         filter_config = {}

#     # Apply filtering to combined log data
#     drop_conditions = filter_config.get("drop_conditions", {})
#     for column, values_to_drop in drop_conditions.items():
#         if column in combined_log_data.columns:
#             combined_log_data = combined_log_data[~combined_log_data[column].isin(values_to_drop)]

#     # Now merge logs for RDP detection
#     combined_log_data = merge_logs_based_on_rdp(combined_log_data, device_logs)        

#     # Create image dataframe from image_data
#     imageDataFrame = pd.DataFrame(image_data, columns=['ImageLocation', 'LogTime'] if 'ImageLocation' in combined_log_data.columns else ['pngFile', 'strTime'])

#     # Group the combined logs and calculate total time
#     results_list = [
#         [device_name, windowTitle, application if not pd.isna(application) else "Desktop", project, sub_project, customer, time_spent_sec / 3600, date]
#         for (device_name, windowTitle, application, project, sub_project, customer, date), time_spent_sec in total_time.items()
#     ]
#     results = pd.DataFrame(results_list, columns=['Device Name', 'WindowTitle', 'Application', 'Project', 'Sub-Project', 'Customer', 'Total Time (hours)', 'strDate'])

#     # Return both the results and imageDataFrame
#     return results, combined_log_data, device_logs
#     # return results, imageDataFrame
#     # return combined_log_data, pd.DataFrame(image_data, columns=['ImageLocation', 'LogTime'] if 'ImageLocation' in combined_log_data.columns else ['pngFile', 'strTime'])


def load_data_updated(csv_directory, selected_employee_config, keyword_mapping, start_date=None, end_date=None, start_time="13:00", end_time="05:00"):
    total_time = defaultdict(float)
    combined_log_data = pd.DataFrame()  # To combine logs from multiple days
    device_logs = {}  # To store logs per device

    # Convert start_time and end_time to datetime.time objects
    try:
        start_time_obj = datetime.strptime(start_time, "%H:%M").time()
        end_time_obj = datetime.strptime(end_time, "%H:%M").time()
    except ValueError as e:
        raise ValueError("Invalid time format for start_time or end_time. Please use 'HH:MM' format.") from e

    # Check if the time range spans across two days
    time_spans_two_days = start_time_obj > end_time_obj

    # Create a date range if start_date and end_date are provided; otherwise, we'll list available date folders
    if start_date and end_date:
        try:
            date_range = pd.date_range(start=start_date, end=end_date).to_pydatetime()
        except Exception as e:
            raise ValueError("Invalid date format for start_date or end_date. Please use 'YYYY-MM-DD'.") from e
    else:
        date_range = None

    # Build a mapping from username (lowercase) to a set of allowed PC names (lowercase) for the selected employee
    employee_pc_mapping = {}
    for pc in selected_employee_config.get("PCs", []):
        user_name = pc.get("USER_NAME")
        pc_name = pc.get("PC_NAME")
        if user_name and pc_name:
            uname = user_name.lower()
            if uname not in employee_pc_mapping:
                employee_pc_mapping[uname] = set()
            employee_pc_mapping[uname].add(pc_name.lower())

    # Iterate only over the folders for the selected employee's usernames
    for uname, allowed_pc_names in employee_pc_mapping.items():
        user_folder = os.path.join(csv_directory, uname)
        if not os.path.exists(user_folder):
            continue  # Skip if the username folder doesn't exist

        # Determine date folders to process:
        if date_range is not None and len(date_range) > 0:
            date_folders = [f"{d.year}_{d.month}_{d.day}" for d in date_range]
        else:
            date_folders = [d for d in os.listdir(user_folder) if os.path.isdir(os.path.join(user_folder, d))]

        for folder in date_folders:
            date_folder_path = os.path.join(user_folder, folder)
            if not os.path.exists(date_folder_path):
                continue

            # ---------------------------
            # Process Old Format (log.csv directly under date folder)
            # ---------------------------
            # Check for both lowercase and uppercase log file names
            old_log_file_path = os.path.join(date_folder_path, "log.csv")
            if not os.path.exists(old_log_file_path):
                old_log_file_path = os.path.join(date_folder_path, "Log.csv")

            if os.path.exists(old_log_file_path):
                try:
                    day_log_data = process_log_file(old_log_file_path, keyword_mapping, total_time, start_time_obj, "23:59")
                    mapping_df = pd.read_csv(r"resource\Account.csv")
                    exceldf = get_top_titles_df(old_log_file_path, selected_employee_config["EMP_NAME"])
                    browser_df = get_top_browser_titles_df(old_log_file_path, selected_employee_config["EMP_NAME"])
                    codeeditor_df = get_top_CodeEditor_titles_df(old_log_file_path, selected_employee_config["EMP_NAME"])
                    combined_df = pd.concat([mapping_df , exceldf , browser_df , codeeditor_df], ignore_index=True)
                    day_log_data = remap_window_title(day_log_data,combined_df,selected_employee_config["EMP_NAME"])
                    day_log_data['Device Name'] = "Unknown Device"
                    combined_log_data = pd.concat([combined_log_data, day_log_data], ignore_index=True)

                    # If the time range spans two days, check next day's folder (old format)
                    if time_spans_two_days:
                        try:
                            year, month, day = map(int, folder.split("_"))
                            current_date = datetime(year, month, day)
                            next_date = current_date + timedelta(days=1)
                            next_folder = f"{next_date.year}_{next_date.month}_{next_date.day}"
                            next_day_folder_path = os.path.join(user_folder, next_folder)
                            # Check for both lowercase and uppercase log file names
                            next_day_log_file = os.path.join(next_day_folder_path, "log.csv")
                            if not os.path.exists(next_day_log_file):
                                next_day_log_file = os.path.join(next_day_folder_path, "Log.csv")

                            if os.path.exists(next_day_log_file):
                                next_day_log_data = process_log_file(next_day_log_file, keyword_mapping, total_time, "00:00", end_time_obj, time_spans_two_days=True)
                                mapping_df = pd.read_csv(r"resource\Account.csv")
                                exceldf = get_top_titles_df(next_day_log_file, selected_employee_config["EMP_NAME"])
                                browser_df = get_top_browser_titles_df(next_day_log_file, selected_employee_config["EMP_NAME"])
                                codeeditor_df = get_top_CodeEditor_titles_df(next_day_log_file, selected_employee_config["EMP_NAME"])
                                combined_df = pd.concat([mapping_df , exceldf , browser_df , codeeditor_df], ignore_index=True)
                                next_day_log_data = remap_window_title(next_day_log_data,combined_df,selected_employee_config["EMP_NAME"])
                                next_day_log_data['Device Name'] = "Unknown Device"
                                combined_log_data = pd.concat([combined_log_data, next_day_log_data], ignore_index=True)
                        except Exception as e:
                            print(f"Error processing next day log for old format in folder {folder}: {e}")
                except Exception as e:
                    print(f"Error processing old format log file in folder {folder}: {e}")
            else:
                # ---------------------------
                # Process New Format (PC folder inside date folder)
                # ---------------------------
                for pc_folder in os.listdir(date_folder_path):
                    pc_folder_path = os.path.join(date_folder_path, pc_folder)
                    if os.path.isdir(pc_folder_path) and (pc_folder.lower() in allowed_pc_names):
                        # Check for both lowercase and uppercase log file names
                        log_file_path = os.path.join(pc_folder_path, "log.csv")
                        if not os.path.exists(log_file_path):
                            log_file_path = os.path.join(pc_folder_path, "Log.csv")

                        if os.path.exists(log_file_path):
                            try:
                                day_log_data = process_log_file(log_file_path, keyword_mapping, total_time, start_time_obj, "23:59")
                                mapping_df = pd.read_csv(r"resource\Account.csv")
                                if not day_log_data.empty:
                                    exceldf = get_top_titles_df(log_file_path, selected_employee_config["EMP_NAME"])
                                    browser_df = get_top_browser_titles_df(log_file_path, selected_employee_config["EMP_NAME"])
                                    codeeditor_df = get_top_CodeEditor_titles_df(log_file_path, selected_employee_config["EMP_NAME"])
                                    combined_df = pd.concat([mapping_df , exceldf , browser_df , codeeditor_df], ignore_index=True)
                                    day_log_data = remap_window_title(day_log_data,combined_df,selected_employee_config["EMP_NAME"])
                                    day_log_data['Device Name'] = pc_folder
                                    device_logs[pc_folder] = day_log_data
                                    day_log_data['RealTime'] = day_log_data['Time']
                                    combined_log_data = pd.concat([combined_log_data, day_log_data], ignore_index=True)
                                # Process next day's log if time spans two days
                                if time_spans_two_days:
                                    try:
                                        year, month, day = map(int, folder.split("_"))
                                        current_date = datetime(year, month, day)
                                        next_date = current_date + timedelta(days=1)
                                        next_folder = f"{next_date.year}_{next_date.month}_{next_date.day}"
                                        next_day_pc_folder = os.path.join(user_folder, next_folder, pc_folder)
                                        # Check for both lowercase and uppercase log file names
                                        next_day_log_file = os.path.join(next_day_pc_folder, "log.csv")
                                        if not os.path.exists(next_day_log_file):
                                            next_day_log_file = os.path.join(next_day_pc_folder, "Log.csv")

                                        if os.path.exists(next_day_log_file):
                                            next_day_log_data = process_log_file(next_day_log_file, keyword_mapping, total_time, "00:00", end_time_obj, time_spans_two_days=True)
                                            mapping_df = pd.read_csv(r"resource\Account.csv")
                                            if not next_day_log_data.empty:
                                                exceldf = get_top_titles_df(next_day_log_file, selected_employee_config["EMP_NAME"])
                                                browser_df = get_top_browser_titles_df(next_day_log_file, selected_employee_config["EMP_NAME"])
                                                codeeditor_df = get_top_CodeEditor_titles_df(next_day_log_file, selected_employee_config["EMP_NAME"])
                                                combined_df = pd.concat([mapping_df , exceldf , browser_df , codeeditor_df], ignore_index=True)
                                                next_day_log_data = remap_window_title(next_day_log_data,combined_df,selected_employee_config["EMP_NAME"])
                                                next_day_log_data['Device Name'] = pc_folder
                                                # Adjust timestamps to subtract one day
                                                next_day_log_data['RealTime'] = next_day_log_data['Time']
                                                next_day_log_data['Time'] = next_day_log_data['Time'] - pd.Timedelta(days=1)
                                                combined_log_data = pd.concat([combined_log_data, next_day_log_data], ignore_index=True)
                                    except Exception as e:
                                        print(f"Error processing next day log for device {pc_folder} in folder {folder}: {e}")
                            except Exception as e:
                                print(f"Error processing log file for device {pc_folder} in folder {folder}: {e}")

    # ---------------------------
    # Apply Filtering
    # ---------------------------
    try:
        filter_config = monHelper.ReadJSON("resource/logdrop.json")
    except FileNotFoundError:
        print("Filter configuration file 'logdrop.json' not found.")
        filter_config = {}
    drop_conditions = filter_config.get("drop_conditions", {})
    for column, values_to_drop in drop_conditions.items():
        if column in combined_log_data.columns:
            combined_log_data = combined_log_data[~combined_log_data[column].isin(values_to_drop)]
    try:
        combined_log_data = monHelper.remove_lock_screen_apps(combined_log_data)
        if 'Application' in combined_log_data.columns:
            combined_log_data['Application'] = combined_log_data['Application'].replace('', 'Unknown').fillna('Unknown')
    except:
        combined_log_data = combined_log_data
        print(traceback.print_exc())

    # Merge logs for RDP detection
    combined_log_data = merge_logs_based_on_rdp(combined_log_data, device_logs, selected_employee_config)      #------------------------------------------------------------------------

    # Note: Image data is now extracted directly from combined_log_data in the image viewer

    # Group the combined logs and calculate total time per (device, window, application, etc.)
    results_list = [
        [device_name, windowTitle, application if not pd.isna(application) else "Desktop",
         project, sub_project, customer, time_spent_sec / 3600, date]
        for (device_name, windowTitle, application, project, sub_project, customer, date), time_spent_sec in total_time.items()
    ]
    results = pd.DataFrame(results_list, columns=[
        'Device Name', 'WindowTitle', 'Application', 'Project', 'Sub-Project', 'Customer', 'Total Time (hours)', 'strDate'
    ])

    return results, combined_log_data, device_logs


def get_matrices(combined_log_data, device_name):

    # Defining variables
    total_working_on_break_in_sec = 0
    total_half_day_leave_in_sec = 0
    total_working_on_other_system_in_sec = 0
    total_working_on_system_down_in_sec = 0
    total_working_on_interview_in_sec = 0
    total_working_on_meeting_in_sec = 0
    total_leave_in_sec = 0

    combined_log_data_original = combined_log_data.copy(deep=True)
    combined_log_data_original['BreakDetails'] = (combined_log_data_original['BreakDetails'].fillna('{}'))

    combined_log_data_original['BreakDetails_dict'] = combined_log_data_original['BreakDetails'].apply(ast.literal_eval)
    # mark any row whose BreakDetails_dict == the previous row AND whose TimeDiffSec == 0
    combined_log_data_original['is_split_second'] = ((combined_log_data_original['BreakDetails_dict'] == combined_log_data_original['BreakDetails_dict'].shift(1)) & (combined_log_data_original['TimeDiffSec'] == 0))


    if device_name == "":
        total_hours_worked = combined_log_data_original["TimeSpentActual"].sum()/60/60
    elif device_name != "":
        combined_log_data_original_device_filtered = combined_log_data_original[combined_log_data_original["PCName"] == device_name]
        total_hours_worked = combined_log_data_original_device_filtered["TimeSpentActual"].sum()/60/60
    
    try:
        if device_name == "":
            for indexParent, dictBreak in enumerate(combined_log_data_original['BreakDetails_dict']):
                if combined_log_data_original['is_split_second'].iloc[indexParent]:
                    continue
                if dictBreak != dict():
                    if len(dictBreak["strTimeInMin"]) == 1:
                        npFloatTimeSpent = combined_log_data_original['TimeSpentActual'].iloc[indexParent]
                        npFloatTimeSpentBetweenLogs = combined_log_data_original['TimeSpentActual'].iloc[indexParent]
                    
                    for index, strBreakType in enumerate(dictBreak['BreakType']):
                        try:
                            # Handling of multiple row creation but in process_rows when multiple meeting topics are there
                            npFloatTimeSpentBetweenLogs = combined_log_data_original['TimeSpentActual'].iloc[indexParent]
                            if npFloatTimeSpentBetweenLogs == 0:
                                continue
                            
                            if len(dictBreak["strTimeInMin"]) > 1:
                                npFloatTimeSpent = int(dictBreak["strTimeInMin"][index])*60
                            
                            if strBreakType == "Break":
                                total_working_on_break_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "Half Day Leave":
                                total_half_day_leave_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "Working On Other System":
                                total_working_on_other_system_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "System Down":
                                total_working_on_system_down_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "Interview":
                                total_working_on_interview_in_sec += int(npFloatTimeSpent)
                            elif strBreakType in ("Meeting", "DND Customer"):
                                total_working_on_meeting_in_sec += int(npFloatTimeSpent)
                        except:
                            print(traceback.print_exc())

        elif device_name != "":
            for indexParent, dictBreak in enumerate(combined_log_data_original_device_filtered['BreakDetails_dict']):
                if combined_log_data_original_device_filtered['is_split_second'].iloc[indexParent]:
                    continue
                if dictBreak != dict():
                    if len(dictBreak["strTimeInMin"]) == 1:
                        npFloatTimeSpent = combined_log_data_original_device_filtered['TimeSpentActual'].iloc[indexParent]
                    
                    for index, strBreakType in enumerate(dictBreak['BreakType']):
                        try:
                            # Handling of multiple row creation but in process_rows when multiple meeting topics are there 
                            npFloatTimeSpentBetweenLogs = combined_log_data_original_device_filtered['TimeSpentActual'].iloc[indexParent]
                            if npFloatTimeSpentBetweenLogs == 0:
                                continue

                            if len(dictBreak["strTimeInMin"]) > 1:
                                npFloatTimeSpent = int(dictBreak["strTimeInMin"][index])*60
                            
                            if strBreakType == "Break":
                                total_working_on_break_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "Half Day Leave":
                                total_half_day_leave_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "Working On Other System":
                                total_working_on_other_system_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "System Down":
                                total_working_on_system_down_in_sec += int(npFloatTimeSpent)
                            elif strBreakType == "Interview":
                                total_working_on_interview_in_sec += int(npFloatTimeSpent)
                            elif strBreakType in ("Meeting_", "DND Customer"):
                                total_working_on_meeting_in_sec += int(npFloatTimeSpent)
                        except:
                            print(traceback.print_exc())
        total_leave_in_sec = combined_log_data_original.loc[combined_log_data_original["Application"] == "Leave","TimeSpentActual"].sum()
        total_leave_in_hours = total_leave_in_sec / 3600         
        total_working_on_meeting_in_hours = total_working_on_meeting_in_sec/60/60
        total_working_on_break_in_hours = total_working_on_break_in_sec/60/60
        total_half_day_leave_in_hours = total_half_day_leave_in_sec/60/60
        total_working_on_other_system_in_hours = total_working_on_other_system_in_sec/60/60
        total_working_on_system_down_in_hours = total_working_on_system_down_in_sec/60/60
        total_working_on_interview_in_hours = total_working_on_interview_in_sec/60/60
         # NEW: compute system hours
        system_hours_worked = (total_hours_worked - total_working_on_break_in_hours - total_working_on_other_system_in_hours - total_working_on_system_down_in_hours
            - total_working_on_interview_in_hours - total_working_on_meeting_in_hours - total_leave_in_hours)
    except:
        print(traceback.print_exc())
    
    return total_hours_worked, total_working_on_break_in_hours, total_working_on_other_system_in_hours, total_working_on_system_down_in_hours, total_working_on_interview_in_hours, total_leave_in_hours, system_hours_worked, total_half_day_leave_in_hours