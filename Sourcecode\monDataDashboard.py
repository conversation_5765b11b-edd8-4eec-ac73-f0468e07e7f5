import streamlit as st
# Set page config FIRST (must be the first Streamlit command)
st.set_page_config(page_title="Monitoring Dashboard", layout="wide")

import sys
import ast
sys.path.append('.')
import pandas as pd
import json
import os
import calendar
from datetime import datetime, timedelta
import plotly.express as px
import plotly.graph_objects as go
import random
import numpy as np
import warnings
import binascii
from collections import defaultdict
import itertools
from streamlit_cookies_manager.encrypted_cookie_manager import EncryptedCookieManager
from imageviewer import imageViewer
from monDataLoad import load_data_updated, get_matrices
import monDashboardHelper as monHelper
from MonUserMail import get_extended_color_map
from MonUserMail import generate_distinct_color
from Generatemailreport import show_report_page  
from LeaveReport import show_leave_report_page
# from AdminPanel import render_admin_panel


warnings.filterwarnings("ignore")

# Initialize cookie manager
cookies = EncryptedCookieManager(
    prefix="mon_dashboard_",
    password="river123" 
)
if not cookies.ready():
    st.stop()


hide_page_navigation = """
       <style>
       [data-testid="stSidebarNav"] {display: none;}
       </style>
   """
st.markdown(hide_page_navigation, unsafe_allow_html=True)

# --- Login configuration -----------------------------------------------
def load_credentials(path: str = "resource/credentials.json") -> dict:
    """Load the username-password map from a JSON file."""
    try:
        with open(path, "r", encoding="utf-8") as fp:
            creds = json.load(fp)
            if not isinstance(creds, dict):
                raise ValueError("Credential file must contain a JSON object.")
            return creds
    except FileNotFoundError:
        st.error(f"Credential file not found → {path}")
        st.stop()
    except Exception as exc:
        st.error(f"Could not read credential file: {exc}")
        st.stop()

_CREDENTIALS = load_credentials()   # now driven by the JSON file


# --- Initialize session state immediately after imports ---
if "authenticated" not in st.session_state:
    st.session_state["authenticated"] = False

if "dashboard_user" not in st.session_state:
    st.session_state["dashboard_user"] = None
    
if "show_report" not in st.session_state:
    st.session_state["show_report"] = False
    
if "show_leave_report" not in st.session_state:
    st.session_state["show_leave_report"] = False

def _login_screen():
    # Track current view (login or reset)
    if "view" not in st.session_state:
        st.session_state["view"] = "login"

    # Show reset password form
    if st.session_state["view"] == "reset":
        st.title("🔁 Reset Your Password")
        reset_user = st.text_input("Username", key="reset_user")
        old_pwd = st.text_input("Old Password", type="password", key="old_pwd")
        new_pwd = st.text_input("New Password", type="password", key="new_pwd")

        if st.button("Reset Password"):
            try:
                stored_hex = _CREDENTIALS.get(reset_user)
                if not stored_hex:
                    st.error("❌ Username does not exist.")
                else:
                    stored_pwd = bytes.fromhex(stored_hex).decode('utf-8')
                    if old_pwd != stored_pwd:
                        st.error("❌ Old password is incorrect.")
                    else:
                        _CREDENTIALS[reset_user] = new_pwd.encode('utf-8').hex()
                        with open("resource/credentials.json", "w", encoding="utf-8") as fp:
                            json.dump(_CREDENTIALS, fp, indent=4)
                        st.success("✅ Password changed successfully. Please log in.")
                        st.session_state["view"] = "login"
            except Exception as e:
                st.error(f"❌ Failed to update credentials: {e}")

        if st.button("🔙 Back to Login"):
            st.session_state["view"] = "login"
        return

    # Else show login screen
    st.title("🔒 Monitoring Dashboard Login")
    user = st.text_input("Username")
    pwd = st.text_input("Password", type="password")

    if st.button("Log In"):
        stored_hex_pwd = _CREDENTIALS.get(user)
        if stored_hex_pwd:
            try:
                decoded_pwd = bytes.fromhex(stored_hex_pwd).decode('utf-8')
                if decoded_pwd == pwd:
                    st.session_state["authenticated"] = True
                    st.session_state["dashboard_user"] = user
                    expiration = datetime.now() + timedelta(days=7)
                    auth_data = {"username": user, "expiration": expiration.isoformat()}
                    cookies["auth_token"] = json.dumps(auth_data)
                    st.rerun()
                else:
                    st.error("Invalid username / password")
            except ValueError:
                st.error("Corrupted password data.")
        else:
            st.error("Invalid username / password")
        
    if st.button("Click here to Reset Password"):
        st.session_state["view"] = "reset"
        st.rerun()


# Initialize session state
if "authenticated" not in st.session_state:
    st.session_state["authenticated"] = False

# Check for existing cookie if not authenticated
if "auth_token" in cookies and not st.session_state["authenticated"]:
    try:
        auth_data = json.loads(cookies["auth_token"])
        username = auth_data["username"]
        expiration = datetime.fromisoformat(auth_data["expiration"])
        if expiration > datetime.now():
            st.session_state["authenticated"] = True
            st.session_state["dashboard_user"] = username  
    except Exception as e:
        print(f"Error parsing auth cookie: {e}")
# app_color_palette = {

#     # Customer Color Mapping
#     'abha': '#7d6899',
#     'lecroy':'#ad5ea1',
#     'real' : '#028ca3',
#     'riveredge' : '#028ca3',
#     'real - developer':'#028ca3',
#     'ikio': '#98b048',
#     'Real':'#189fba',
    
#     # Projects Color Mapping
#     'Monitoring':'#9e2e2e',
#     'Accuvelocity':'#a8ad5e',
    
#     # Application Color Mapping
#     'Break':'#242323',
#     'Desktop':'#242323',
#     'Skype': '#87CEEB',  # Light blue
#     'Google Chrome': '#f54a0c',  # Orange
#     'Browser': '#f54a0c',  # Orange
#     'Zoom Meeting': '#295ee3',  # Red
#     'Remote Desktop Connection': '#9370DB',  # Purple
#     'RDP': '#9370DB',  # Purple
#     'Excel': '#32CD32',  # Green
#     'Visual Studio Code': '#a8328b',
#     'Code Editor': '#a8328b',
#     "File Explorer":"#F1C232",
# }
color_palette = monHelper.ReadJSON("Sourcecode/color_palette.json")
customer_palette = color_palette["customer"]
project_palette = color_palette["project"]
application_palette = color_palette["application"]
filter_config = monHelper.ReadJSON("resource/logdrop.json")
drop_conditions = filter_config.get("drop_conditions", {})


# Function to generate vibrant colors using predefined color palettes
def get_vibrant_colors(n):
    # Define the palettes to use
    alphabet_palette = px.colors.qualitative.Plotly
    g10_palette = px.colors.qualitative.Safe
    set3_palette = px.colors.qualitative.Set3
    
    # Combine all palettes
    all_colors = alphabet_palette + g10_palette + set3_palette
    
    # Remove colors that are already in use
    available_colors = [color for color in all_colors if color not in color_palette.values()]
    
    # Shuffle the available colors to ensure randomness
    random.shuffle(available_colors)
    
    # Select n unique colors from the available list
    vibrant_colors = available_colors[:n]
    
    return vibrant_colors

# Function to get the color for an application, project, or customer
def get_application_color(app_name, total_items):
    # Convert the app name to lowercase to ensure case-insensitive matching
    app_name = str(app_name).lower().strip()

    # Check if the app has a predefined color
    if app_name in color_palette:
        return color_palette[app_name]
    # print("color_palette",color_palette)
    # print("Apps Name",app_name)
    # print(app_color_palette,app_name)
    # Generate vibrant colors dynamically if needed
    vibrant_colors = get_vibrant_colors(total_items)
    
    # If no predefined color, assign a new vibrant color from the generated palette
    return vibrant_colors.pop()

def get_employee_config_by_name(userdata: dict, selected_emp_name: str) -> dict:
    for emp_id, config in userdata.items():
        if config.get("EMP_NAME", "").strip().lower() == selected_emp_name.strip().lower():
            return config
    return None  # Or raise an exception if preferred
def get_parent_application(app: str) -> str:
    if app in ["Break", "Desktop"] or app.startswith("Meeting_") or app.startswith("DND Customer_"):
        return app
    return app.split("_")[0]
# Custom CSS for adding visible borders and styling the grid
def apply_custom_styles():
    st.markdown("""
        <style>
        .kpi-box {
            border: 1px solid #444; /* Visible border for each KPI box */
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 10px;
            text-align: center;
            background-color: #1e1f21; /* Dark background for a professional look */
            color: #E0E0E0; /* Light text color for readability */
        }
        .kpi-label {
            font-size: 16px;
            font-weight: bold;
            color: #d9d9d9;
        }
        .kpi-value {
            font-size: 25px;
            font-weight: bold;
            color: #316aa3; /* Bright cyan for the KPI values */
        }
        </style>
    """, unsafe_allow_html=True)


# Function to read the changelog file
def read_changelog(filepath):
    try:
        with open(filepath, 'r') as file:
            return file.read()
    except Exception as e:
        return "Error loading changelog."

# Function to convert total hours into hours and minutes
def hours_to_hours_minutes_inFormat(hours):
    if hours > 24:
        int_hours = int(hours - 24)
    else:
        int_hours = int(hours)
    minutes = int((hours - int(hours)) * 60)
    return f"{int_hours} hrs {minutes} min"
    
# Function to convert total hours into hours and minutes
def hours_to_hours_minutes(hours):
    int_hours = int(hours)
    minutes = int((hours - int_hours) * 60)
    return f"{int_hours} hrs {minutes} min"

def hours_to_hours_minutes_seconds(hours):
    int_hours = int(hours)
    total_minutes = int(hours * 60)
    minutes = total_minutes % 60
    seconds = int((hours * 3600) % 60)
    return f"{int_hours} hrs {minutes} min {seconds} sec"

def hours_to_minutes_seconds(hours):
    if pd.isna(hours):
        return "N/A"
    total_seconds = int(hours * 3600)
    minutes = total_seconds // 60
    seconds = total_seconds % 60
    return f"{minutes} mins {seconds} sec"

def generate_distinct_color(existing_colors: set) -> str:
    """Generates a random hex color not in existing_colors."""
    while True:
        color = "#%06x" % random.randint(0x100000, 0xFFFFFF)
        if color.lower() not in existing_colors:
            return color

def get_extended_color_map(user_df, base_color_map):
    extended_map = {}
    used_colors = set(v.lower() for v in base_color_map.values() if v)  # ignore blanks

    # Special forced colors
    extended_map["Break"] = "#d3d3d3"
    extended_map["Desktop"] = "#d3d3d3"

    meeting_color = base_color_map.get("Meeting", "#452003")
    DND_color = base_color_map.get("DND", "#FF0000")

    for app in user_df["Application"].unique():
        if not isinstance(app, str):
            continue

        # 1. Match Meeting_*
        if app.startswith("Meeting_"):
            extended_map[app] = meeting_color
            
        if app.startswith("DND Customer_"):
            extended_map[app] = DND_color
        
        # 2. Suffix match: ends with _<base_key>
        elif any(app.startswith(f"{base_key}_") for base_key in base_color_map):
            # pick the first matching key
            for base_key, color in base_color_map.items():
                if app.startswith(f"{base_key}_"):
                    extended_map[app] = color
                    used_colors.add(color.lower())
                    break
                
        # 2. App with empty string color in base dict
        elif app in base_color_map and base_color_map[app] == "":
            new_color = generate_distinct_color(used_colors)
            extended_map[app] = new_color
            used_colors.add(new_color.lower())
              
        # 3. App is in base map with valid color
        elif app in base_color_map and base_color_map[app]:
            extended_map[app] = base_color_map[app]
            used_colors.add(base_color_map[app].lower())

        # 4. Completely new/unknown app
        elif app not in base_color_map:
            new_color = generate_distinct_color(used_colors)
            extended_map[app] = new_color
            used_colors.add(new_color.lower())

    return extended_map


def show_dashboard(str_username):
    # apply_custom_styles()
        
    # Load user data
    with open("resource/UserDataNEW.json", "r") as file:
        dictUserDataAdmin = json.load(file)

    strDashboardUserName = st.session_state.get("dashboard_user", "")
    bIsAdmin = False

    for empId, empData in dictUserDataAdmin.items():
        if empData.get("EMP_NAME", "").strip().lower() == strDashboardUserName.strip().lower():
            bIsAdmin = empData.get("isAdmin", False)
            break

    # Check if Show Report was clicked via URL param
    if st.query_params.get("show_report") == "1":
        st.session_state["show_report"] = True
        st.rerun()
    
    if st.query_params.get("show_leave_report") == "1":
        st.session_state["show_leave_report"] = True
        st.rerun()
    
    # In Dashboard page (e.g., main.py or Dashboard.py)
    html_buttons = f'''
    <div style="display: flex; gap: 10px;">
        <a href="?show_report=1" target="_self">
            <button style="padding:6px 12px; font-size:14px;">📊 Show Report</button>
        </a>
         <a href="?show_leave_report=1" target="_self">              
            <button style="padding:6px 12px;font-size:14px;">🗓️ Leave Report</button>
        </a>
        {'<a href="/AdminPanel" target="_self"><button style="padding:6px 12px; font-size:14px;">⚙️ Open Admin Panel</button></a>' if bIsAdmin else ''}
    </div>
    '''
    st.markdown(html_buttons, unsafe_allow_html=True)

    # Initialize session state for selected member and base directory if not already set
    if 'selected_member' not in st.session_state:
        st.session_state['selected_member'] = None
        st.session_state['base_directory'] = None

    # Load configuration data
    dict_config_data = monHelper.ReadJSON("resource/Mon_config.json")
    for strDefaultDirPath in dict_config_data['MainPath']:
        if os.path.exists(strDefaultDirPath):
            default_base_directory = strDefaultDirPath
            break
        
    dictGeneralUserInfo = monHelper.ReadJSON(dict_config_data["GeneralAccountsConfig"])
    # default_base_directory = dict_config_data['MainPath']
    userDataPath = dict_config_data['UserDataPath']
    # Path to the JSON mapping file
    mappingFilePath = dict_config_data['mappingFilePath']
    # Read the JSON mapping file
    keyword_mapping = monHelper.ReadJSON(mappingFilePath)

    # If base directory is not set, set it to default
    if st.session_state['base_directory'] is None:
        st.session_state['base_directory'] = default_base_directory


    # Top section for title and buttons
    st.title("Monitoring Dashboard")
    # Sidebar for filters
    with st.sidebar:
        # Custom style to remove top margin space
        st.markdown(
            '''
            <style>
                .st-emotion-cache-16txtl3 {padding: 1rem 1.5rem;}
                div.stButton > button:first-child {margin-top: 10px;}
            </style>
            ''', 
            unsafe_allow_html=True
        )
        st.header("Version 1.12.********")# First column for the "Filters" header
        st.write(f"Logged in as: {st.session_state['dashboard_user']}")
        if st.button("Log Out"):
            st.session_state["authenticated"] = False
            st.session_state["dashboard_user"] = None
            if "auth_token" in cookies:
                cookies["auth_token"] = ""  # Clear the cookie
            st.rerun()
        st.markdown("---")
        # st.markdown("---")
        
        st.header("Filters")# First column for the "Filters" header
        
        st.markdown("---")
        
        st.header("Department")
 
        user_data = monHelper.ReadJSON(userDataPath)
        # List of users who left the organization
        users_left_org = [
            'imdad', 'pavan','kaushal',
            'jignesh', 'mihir', 'vishal', 'sagar', 
            'rushabh.shah', 'darshan', 'sparkle', 'stavan'
        ]
        lsTeamMembersWithDepartments = monHelper.MSGetAllTeamMembers(strUserName=str_username, dictAllUserData=user_data)
        lsTeamMembersWithDepartments = [(name, dept) for name, dept in lsTeamMembersWithDepartments if dept is not None]
        # Get all team members
        departments = list(set([dept for _, dept in lsTeamMembersWithDepartments])) 
        departments.insert(0, "All") # Add "All" to the list of departments
        
        default_department = 'All'
        selected_department = st.selectbox("Select Department", options=departments,index=departments.index(default_department))
        if selected_department == "All":
          filtered_team_members = lsTeamMembersWithDepartments
        else:
            filtered_team_members = [(member, dept) for member, dept in lsTeamMembersWithDepartments if dept == selected_department]
            
        current_team_members = sorted([
            next(
                (emp_data["EMP_NAME"] for emp_id, emp_data in user_data.items() if emp_data["EMP_NAME"].lower() == member.lower()),
                member
            ) for member, dept in filtered_team_members if member.lower() not in users_left_org
        ], key=lambda x: x.lower())

        # Get employees who left the organization under the selected employee
        former_team_members = sorted([emp_data["EMP_NAME"]for emp_id, emp_data in user_data.items()if emp_data["EMP_NAME"].lower() in users_left_org and any(manager in emp_data.get("Manager", []) for manager in current_team_members + [str_username])], key=lambda x: x.lower())

        former_team_members = [f"{name} - Left" for name in former_team_members]
        # Combine current members first, then former members
        lsTeamMembers = current_team_members + former_team_members
        st.markdown("---")
        
        id_to_name_map = {emp_id: emp_data["EMP_NAME"] for emp_id, emp_data in user_data.items()}

        # Replace IDs with names in the member list
        lsTeamMembersWithDisplayNames = [(id_to_name_map.get(member, member), member)for member in lsTeamMembers]
        
        # Sort by display name (first element in tuple)
        lsTeamMembersWithDisplayNames = sorted(lsTeamMembersWithDisplayNames, key=lambda x: x[0])
        st.subheader("Member List")
        
        use_current = st.checkbox("Show Current Employees", value=True)
        # 2) Filter the member list based on the checkbox state
        if use_current:
            # Show only current (no " - Left" in name)
            filtered_list = [
                (display_name, member_id)
                for (display_name, member_id) in lsTeamMembersWithDisplayNames
                if " - Left" not in display_name
            ]
        else:
            # Show only past (contains " - Left" in name)
            filtered_list = [(display_name, member_id)for (display_name, member_id) in lsTeamMembersWithDisplayNames if " - Left" in display_name]
        
        if filtered_list:
            selected_member = st.selectbox("Select Member", options=[display_name for (display_name, _) in filtered_list], key="change_member")

            # Get the corresponding ID of the selected display name
            selected_member = next(
                (member_id for display_name, member_id in filtered_list if display_name == selected_member),
                None
            )
            
            if selected_member:
                # Remove the " - Left" tag from the selected_member before further processing
                selected_member = selected_member.replace(" - Left", "").strip()
        else:
            selected_member = None

        # Check if the selected member is in the general user accounts mapping
        # if selected_member in dictGeneralUserInfo:
        #     # Display the extra information if available
        #     # st.markdown(f"**Usage Information for {selected_member}:**")
        #     if dictGeneralUserInfo[selected_member]:
        #         for info in dictGeneralUserInfo[selected_member]:
        #             if info:
        #                 st.markdown(f"- {info}")
        #     else: 
        #         st.markdown(f"- No Information found.")
        # Update session state and directory path
        if st.session_state['selected_member'] != selected_member:
            st.session_state['selected_member'] = selected_member
            st.session_state['base_directory'] = os.path.join(default_base_directory, selected_member)

        # if st.session_state['selected_member'] != selected_member:
        #     st.session_state['selected_member'] = selected_member
        #     st.session_state['base_directory'] = os.path.join(default_base_directory, selected_member)
        
        st.write("\n" * 10)
        
        st.markdown("---")
        
        # Specific Date Filter
        st.subheader("Date")
        objPreviousDayDate = datetime.today().date() - timedelta(days=1)

        if 'selected_date' not in st.session_state:
            st.session_state['selected_date'] = objPreviousDayDate
        st.session_state['selected_date'] = st.date_input("Select a Date", value=st.session_state['selected_date'])
                
        # Format the selected date to include the day of the week
        formatted_date = st.session_state['selected_date'].strftime("%Y-%m-%d (%A)")

        # Display the selected date with the day of the week
        st.write(f"Selected Date: {formatted_date}")

        apply_date_filter = st.button("Apply Date Filter")
        st.markdown("---")
        
        # Weekly Filter
        st.subheader("Week")
        if 'selected_week' not in st.session_state:
            st.session_state['selected_week'] = datetime.now().isocalendar()[1]

        # Select year for the selected week
        if 'year_for_week' not in st.session_state:
            st.session_state['year_for_week'] = datetime.now().year
        year_for_week = st.number_input(
            "Select Year for Week",
            min_value=2000,
            max_value=2100,
            value=st.session_state['year_for_week'],
            key='year_for_week'
        )

        # Update the session state only if it is different from the number_input's returned value
        if st.session_state['year_for_week'] != year_for_week:
            st.session_state['year_for_week'] = year_for_week
        
        # Generate week options based on the selected year
        week_options = monHelper.get_week_options(st.session_state['year_for_week'])

        # Dropdown to select the week, displaying the week number and date range
        selected_week_option = st.selectbox(
            "Select Week",
            options=week_options,
            format_func=lambda x: x[1],  # Display the formatted week and date range
            index=st.session_state['selected_week'] - 1,
        )

        # Update session state with the selected week number
        st.session_state['selected_week'] = selected_week_option[0]

        # Display the detailed information for the selected week
        start_date, end_date = monHelper.get_week_date_range(st.session_state['selected_week'], st.session_state['year_for_week'])
        st.write(f"**Selected Week:** {st.session_state['selected_week']} "
                f"({start_date.strftime('%A, %B %d, %Y')} - {end_date.strftime('%A, %B %d, %Y')})")

        apply_week_filter = st.button("Apply Week Filter")
        st.markdown("---")

        # Month Filter
        st.subheader("Month")
        if 'selected_month' not in st.session_state:
            st.session_state['selected_month'] = datetime.now().month

        # Create a list of month names
        months = list(calendar.month_name)[1:]  # This skips the first element, which is empty

        # Use the month names for the selectbox options
        selected_month = st.selectbox(
            "Select Month",
            months,
            index=st.session_state['selected_month'] - 1
        )

        # Update session_state if the selection changes
        if months.index(selected_month) + 1 != st.session_state['selected_month']:
            st.session_state['selected_month'] = months.index(selected_month) + 1

        if 'year_for_month' not in st.session_state:
            st.session_state['year_for_month'] = datetime.now().year
        year_for_month = st.number_input(
            "Select Year for Month", 
            min_value=2000, 
            max_value=2100, 
            value=st.session_state['year_for_month'], 
            key='year_for_month'
        )
        # Update the session state only if it is different from the number_input's returned value
        if st.session_state['year_for_month'] != year_for_month:
            st.session_state['year_for_month'] = year_for_month

        apply_month_filter = st.button("Apply Month Filter")
        st.markdown("---")
        
        
        # # Year Filter
        # st.subheader("Year")
        # if 'selected_year' not in st.session_state:
        #     st.session_state['selected_year'] = datetime.now().year
        # selected_year = st.number_input(
        #     "Select Year", 
        #     min_value=2000, 
        #     max_value=2100, 
        #     value=st.session_state['selected_year'],
        #     key='selected_year'
        # )
        
        # # Update the session state only if it is different from the number_input's returned value
        # if st.session_state['selected_year'] != selected_year:
        #     st.session_state['selected_year'] = selected_year

        # apply_year_filter = st.button("Apply Year Filter")
        # st.markdown("---")

        
        # Date Range Filter
        st.subheader("Date Range")
        if 'start_date' not in st.session_state or 'end_date' not in st.session_state:
            st.session_state['start_date'] = st.session_state['end_date'] = objPreviousDayDate
        st.session_state['start_date'] = st.date_input("Start Date", value=st.session_state['start_date'])
        
        # Format the selected date to include the day of the week
        formatted_start_date = st.session_state['start_date'].strftime("%Y-%m-%d (%A)")

        # Display the selected date with the day of the week
        st.write(f"Selected Start Date: {formatted_start_date}")
        
        st.session_state['end_date'] = st.date_input("End Date", value=st.session_state['end_date'])
        
        # Format the selected date to include the day of the week
        formatted_end_date = st.session_state['end_date'].strftime("%Y-%m-%d (%A)")

        # Display the selected date with the day of the week
        st.write(f"Selected End Date: {formatted_end_date}")
        
        apply_date_range_filter = st.button("Apply Date Range Filter")
        
        # Display the changelog in the sidebar
        st.markdown("---")
        st.subheader("Changelog")
        changelog_content = read_changelog(dict_config_data.get("ChangeLog"))  # Replace with actual path
        with st.expander("View Changelog", expanded=True):
            st.text_area("Changelog Content", changelog_content, disabled=True ,height=200)  # Set height as needed
        

    # Create a main container for the layout
    with st.container():
        # Create two main columns
        col1, col2 = st.columns([5, 2])  # Adjust the ratio as needed for better spacing

        # Column 1: Member Information
        with col1:
            if st.session_state['selected_member'] is not None:
                st.subheader("Member Information")
                
                # Check if the selected member is a general account
                if st.session_state['selected_member'] in dictGeneralUserInfo:
                    # Extract the latest entry from the usage information
                    latest_user_info = dictGeneralUserInfo[st.session_state['selected_member']][-1]
                    latest_user = latest_user_info.split()[0]  # Extract the username from the info string
                    # st.info(f"**Currently Used By:** {latest_user}")
                else:
                    st.info(f"**Selected Member:** {st.session_state['selected_member']}")
                
                selected_member_data = user_data.get(st.session_state['selected_member'], {})

                start_time = selected_member_data.get('starttime', '08:00')
                end_time = selected_member_data.get('endtime', '07:00')
                required_hours = selected_member_data.get('required_work_hours', 8.5)

                # ! To Show information related to Work hours and Required Work hours
                st.write(f"**Work hours:** {start_time} - {end_time}")
                # st.write(f"**Required Work Hours:** {required_hours} hours")

        # Column 2: Time Threshold Selection
        with col2:
            st.subheader("Time Filter Settings", help="Use this section to set a minimum time threshold. Data entries with total time below the selected threshold will be excluded from the analysis.")

            time_threshold_options = ['None', '5 minutes', '10 minutes', '30 minutes', '1 hour', '5 hours']

            time_threshold = st.selectbox(
                "Select minimum time",
                options=time_threshold_options,
                index=0
            )

            # Convert the selected threshold to minutes for easier comparison
            time_threshold_mapping = {
                'None': 0,
                '5 minutes': 5,
                '10 minutes': 10,
                '30 minutes': 30,
                '1 hour': 60,
                '5 hours': 300
            }
            selected_threshold_minutes = time_threshold_mapping[time_threshold]
            apply_threshold_filter = st.button("Apply", help="Apply the selected time threshold to filter the data")
            # st.markdown(
            #     '<a href="/AdminPanel" target="_blank">'
            #     '<button style="padding:6px 12px; font-size:14px;">⚙️ Open Admin Panel</button>'
            #     '</a>',
            #     unsafe_allow_html=True
            # )


        st.write("---")  # Separator line for clarity

    if apply_week_filter:
        # Calculate the start and end dates for the selected week
        start_date, end_date = monHelper.get_week_date_range(st.session_state['selected_week'], st.session_state['year_for_week'])
        
        # Update session state
        st.session_state['start_date'] = start_date
        st.session_state['end_date'] = end_date
    elif apply_date_filter:
        st.session_state['start_date'] = st.session_state['end_date'] = st.session_state['selected_date']
    elif apply_date_range_filter:
        # If the range is applied, directly save the selected range to session state
        st.session_state['start_date'] = st.session_state['start_date']
        st.session_state['end_date'] = st.session_state['end_date']
    elif apply_month_filter:
        # Calculate and save the date range for the selected month to session state
        st.session_state['start_date'], st.session_state['end_date'] = monHelper.get_month_date_range(st.session_state['selected_month'], st.session_state['year_for_month'])
    # elif apply_year_filter:
    #     # Calculate and save the date range for the selected year to session state
    #     st.session_state['start_date'], st.session_state['end_date'] = monHelper.get_year_date_range(st.session_state['selected_year'])
    intIteration = 1
    if apply_threshold_filter or apply_week_filter or apply_date_filter or apply_date_range_filter or apply_month_filter:
        try:
            # Check if dates are set and not None
            if st.session_state['start_date'] and st.session_state['end_date']:
                startDate = st.session_state['start_date']
                endDate = st.session_state['end_date']
                st.info(f'Selected Date: start ({startDate.strftime("%A, %B %d, %Y")}) - end ({endDate.strftime("%A, %B %d, %Y")})')
                selected_employee_config = get_employee_config_by_name(user_data, selected_member) #st.session_state['base_directory']
                allDeviceResults, combined_log_data, device_logs = load_data_updated(
                default_base_directory,
                selected_employee_config,
                keyword_mapping,
                st.session_state['start_date'],
                st.session_state['end_date'],
                start_time=start_time,
                end_time=end_time)
                # st.write(allDeviceResults)
                combined_log_data_original = combined_log_data.copy(deep=True)
                combined_log_data_App = combined_log_data.copy(deep=True)
                combined_log_data_App['TimeSpent_hr'] = combined_log_data_App['TimeSpentActual'] / 3600
                combined_log_data_App["Formatted_app_Working_hours"] = combined_log_data_App["TimeSpent_hr"].apply(hours_to_hours_minutes)
                # combined_log_data_App['StrTime'] = combined_log_data_App['Time'].strftime("%Y-%m-%d (%A)")
                # allDeviceResults = merge_logs_based_on_rdp(allDeviceResults,device_logs)
                # device_wise_results = separate_logs_by_device(combined_log_data)

                # if results is not None and not results.empty and imageDataFrame is not None and not imageDataFrame.empty:
                if allDeviceResults is not None and not allDeviceResults.empty:
                    allDeviceResults = allDeviceResults.dropna(subset=['Device Name'])
                    devices = allDeviceResults['Device Name'].unique()  # Get unique devices
                    
                    if len(devices) == 1:
                        intIteration = 1
                    #Single device: No need for merging, take time directly
                        st.write(f"Single device detected: {devices[0]}")
                    else:
                        #Multiple devices: Apply merging logic to handle RDP and overlaps
                        st.write(f"Multiple devices detected: {len(devices)}")
                        # allDeviceResults = merge_logs_based_on_rdp(allDeviceResults, device_logs)
                        intIteration = len(devices)+1
                        links = [f"[{device}](#{device.replace(' ', '_')})" for device in devices]
                        st.header(f"Device : {', '.join(links)}")
                        devices = allDeviceResults['Device Name'].unique() 
                        results = allDeviceResults # Filter data for this device
                        resultsUpdated = combined_log_data_App

                # for loop 
                first_iteration = True
                for device in range (0,intIteration):
                    if first_iteration:
                        st.subheader("Combined Analysis")
                        results = allDeviceResults
                        resultsUpdated = combined_log_data_App
                        first_iteration = False
                        device_name = ''    # initializing empty string
                    else:
                        # st.header(f"Device: {devices[device-1]}")
                        device_name = devices[device - 1]
                        anchor_id = device_name.replace(" ", "_") 
                        st.markdown(f'<div id="{anchor_id}"></div>', unsafe_allow_html=True)
                        st.header(f"Device: {device_name}")
                        device_data = allDeviceResults[allDeviceResults['Device Name'] == devices[device-1]]
                        results = device_data
                        device_data_updated = combined_log_data_App[combined_log_data_App['Device Name'] == devices[device-1]]
                        resultsUpdated = device_data_updated


                    # if 'Device Name' in allDeviceResults.columns:
                    #     devices = allDeviceResults['Device Name'].unique()  # Get unique devices

                    #     # Create tabs
                    #     tab1, tab2 = st.tabs(["Combined Analysis", "Individual Device Analysis"])

                    #     with tab1:
                    #         st.header("Combined Analysis")
                    #         # Display combined data
                    #         # st.dataframe(allDeviceResults)
                    #         results = allDeviceResults
                    #         # Add your combined analysis logic here

                    #     with tab2:
                    #         st.header("Individual Device Analysis")
                    #         # Device selection
                    #         selected_device = st.selectbox("Select a device", devices)
                    #         # Filter data for the selected device
                    #         # device_data = allDeviceResults[allDeviceResults['Device Name'].str.lower() == selected_device.lower()]
                    #         device_data = device_wise_results.get(selected_device, pd.DataFrame())
                    #         results = device_data 
                    #         # Add your individual device analysis logic here
                    # else:
                    #     st.error("The 'Device Name' column is missing from the data.")

                    # imageDataFrame['pngFile'] = imageDataFrame['pngFile'].str.replace('J:', '\\\\192.168.1.18\\developer_public', regex=False)
                    # Assuming st.session_state['start_date'] and st.session_state['end_date'] are datetime.date objects
                    start_date = st.session_state['start_date']
                    end_date = st.session_state['end_date']
                    
                    
                    # Calculate total days excluding weekends
                    # Initialize a list to store the leave dates
                    today_date = datetime.today().date()
                    lsLeaveDates = []
                    lsExtraWorkDays = []
                    total_days = 0
                    leave_days = 0
                    extra_days = 0
                    lsHolidays = dict_config_data["HolidayList"]
                    # Convert the holiday list to a set of (day, month) tuples
                    lsHolidaysIn_Day_N_Month = set((datetime.strptime(date, '%d-%m-%Y').day, datetime.strptime(date, '%d-%m-%Y').month) for date in lsHolidays)
                    
                    
                    # Loop through each day between start_date and end_date for results 
                    current_date = start_date
                    
                    while current_date <= end_date:
                        # Check if the current date's (day, month) is in the holidays set
                        current_day_month = (current_date.day, current_date.month)
                        
                        allDeviceResults['strDate'] = pd.to_datetime(allDeviceResults['strDate'])
                        current_date = current_date.strftime('%Y-%m-%d')       
                        today_data = allDeviceResults[allDeviceResults['strDate'] == current_date]   
                        total_hours_today = today_data['Total Time (hours)'].sum()
                        current_date =  current_date.split('-')
                        current_date = tuple(int(part) for part in current_date)
                        current_date = datetime(*current_date).date()


                        if monHelper.is_weekend(current_date) and int(total_hours_today) > 0 :           
                            extra_days += 1
                            lsExtraWorkDays.append(f"{current_date.strftime('%d-%m-%Y')} ({current_date.strftime('%A')})")
                            print(extra_days)
                        
                        # Ensure the date is not in the future
                        if current_date < today_date:
                            # Check if the current date is a not  weekend
                            if current_day_month not in lsHolidaysIn_Day_N_Month and not monHelper.is_weekend(current_date):
                                total_days += 1  
                                if current_date not in pd.to_datetime(results['strDate']).dt.date.values:
                                    leave_days += 1
                                    lsLeaveDates.append(f"{current_date.strftime('%d-%m-%Y')} ({current_date.strftime('%A')})")

                                            
                        # Move to the next day
                        current_date += timedelta(days=1)
                        
                    # Create a boolean mask to filter rows
                    mask = results.apply(lambda row: any(
                        str(row[col]) in values for col, values in drop_conditions.items() if col in results
                    ), axis=1)
                    
                    filtered_results = results[~mask].copy()
                    if filtered_results.empty:
                        filtered_results = pd.DataFrame(columns = ['Device Name', 'WindowTitle', 'Application', 'Project', 'Sub-Project', 'Customer', 'Total Time (hours)', 'strDate'])
                    # Compute total hours worked after filtering
                    total_hours_worked = filtered_results['Total Time (hours)'].sum()
                
                    # Calculate total break time (consider all entries with 'Break' in either 'appUsed' or 'Project')
                    if 'BreakDetails' in results.columns:
                        # Apply the filter
                        break_results = results[
                            (results['WindowTitle'].str.contains('Break', case=False) | 
                            results['Project'].str.contains('Break', case=False)) &
                            (results['BreakDetails'] != "Working on Other Device")
                        ]
                    else:
                        # Handle cases where BreakDetails does not exist
                        break_results = results[
                            (results['WindowTitle'].str.contains('Break', case=False) | 
                            results['Project'].str.contains('Break', case=False))
                        ]

                    # ----------------------------------------------------------------------------------------------------------------------- # 
                    
                    # total_hours_worked_without_dropped_logs = total_hours_worked 
                    total_break_time = break_results['Total Time (hours)'].sum()

                    # finding total_working_on_other_system
                    total_working_on_other_system = 0
                    df_combined_log_data_original_only_breaks = combined_log_data_original[combined_log_data_original["BreakDetails"] != '{}']
                    df_combined_log_data_original_only_breaks['BreakDetails_dict'] = df_combined_log_data_original_only_breaks['BreakDetails'].apply(ast.literal_eval)

                    # Filtering dataframe based on device
                    if not device_name == '':
                        df_combined_log_data_original_only_breaks = df_combined_log_data_original_only_breaks[df_combined_log_data_original_only_breaks['PCName'] == device_name]

                    combined_log_data_original['BreakDetails_dict'] = combined_log_data_original['BreakDetails'].apply(ast.literal_eval)

                    # Working on other system
                    try:
                        for indexParent, dictBreak in enumerate(combined_log_data_original['BreakDetails_dict']):
                            if dictBreak != dict():
                                for index, strBreakType in enumerate(dictBreak['BreakType']):
                                    if strBreakType == 'Working On Other System':
                                        total_working_on_other_system += int(combined_log_data_original['TimeSpentActual'].iloc[indexParent])
                            
                        total_working_on_other_system_in_hours = total_working_on_other_system/60/60
                    except:
                        total_working_on_other_system_in_hours = 0

                    # System down
                    total_system_down = 0
                    try:
                        for indexParent, dictBreak in enumerate(combined_log_data_original['BreakDetails_dict']):
                            if dictBreak != dict():
                                for index, strBreakType in enumerate(dictBreak['BreakType']):
                                    if strBreakType == 'System Down':
                                        total_system_down += int(combined_log_data_original['TimeSpentActual'].iloc[indexParent])
                            
                        total_system_down_in_hours = total_system_down/60/60
                    except:
                        total_system_down_in_hours = 0
                    
                    # # Getting total_hours_worked from combined_log_data
                    # if device_name == "":
                    #     total_hours_worked = combined_log_data["TimeSpent"].sum()/60/60
                    

                    total_hours_worked = combined_log_data_original["TimeSpentActual"].sum()/60/60
                    total_break_time = (combined_log_data_original[combined_log_data_original["Project"]=="Break"])["TimeSpentActual"].sum()/60/60

                    # break
                    total_break = 0
                    try:
                        for indexParent, dictBreak in enumerate(combined_log_data_original['BreakDetails_dict']):
                            if dictBreak != dict():
                                for index, strBreakType in enumerate(dictBreak['BreakType']):
                                    if strBreakType == 'Break':
                                        total_break += int(combined_log_data_original['TimeSpentActual'].iloc[indexParent])
                            
                        total_break_in_hours = total_break/60/60
                    except:
                        total_break_in_hours = 0

                    if not device_name == '':
                        total_hours_worked = combined_log_data_original[combined_log_data_original["PCName"] == device_name]["TimeSpentActual"].sum()/60/60
                        filtered_combined_log_data_original = combined_log_data_original[combined_log_data_original["PCName"] == device_name]
                        total_break_time = (filtered_combined_log_data_original[filtered_combined_log_data_original["Project"]=="Break"])["TimeSpentActual"].sum()/60/60

                        # break
                        total_break = 0
                        try:
                            for indexParent, dictBreak in enumerate(filtered_combined_log_data_original['BreakDetails_dict']):
                                if dictBreak != dict():
                                    for index, strBreakType in enumerate(dictBreak['BreakType']):
                                        if strBreakType == 'Break':
                                            total_break += int(filtered_combined_log_data_original['TimeSpentActual'].iloc[indexParent])
                                
                            total_break_in_hours = total_break/60/60
                        except:
                            total_break_in_hours = 0

                        # Filtering Working on other system for specific device
                        total_working_on_other_system = 0
                        try:
                            for indexParent, dictBreak in enumerate(filtered_combined_log_data_original['BreakDetails_dict']):
                                if dictBreak != dict():
                                    for index, strBreakType in enumerate(dictBreak['BreakType']):
                                        if strBreakType == 'Working On Other System':
                                            total_working_on_other_system += int(filtered_combined_log_data_original['TimeSpentActual'].iloc[indexParent])
                                
                            total_working_on_other_system_in_hours = total_working_on_other_system/60/60
                        except:
                            total_working_on_other_system_in_hours = 0

                        # Filtering System down
                        total_working_on_system_down = 0
                        try:
                            for indexParent, dictBreak in enumerate(filtered_combined_log_data_original['BreakDetails_dict']):
                                if dictBreak != dict():
                                    for index, strBreakType in enumerate(dictBreak['BreakType']):
                                        if strBreakType == 'System Down':
                                            total_working_on_system_down += int(filtered_combined_log_data_original['TimeSpentActual'].iloc[indexParent])
                                
                            total_system_down_in_hours = total_working_on_system_down/60/60
                        except:
                            total_system_down_in_hours = 0                        
                        pass

                    
                    total_hours_worked_without_breaks = total_hours_worked - total_break_in_hours - total_working_on_other_system_in_hours - total_system_down_in_hours
                    
                    # -------------------------------------------------------------------------------------------------------------------- #



                    total_hours_worked_new, total_break_in_hours_new, total_working_on_other_system_in_hours_new, total_system_down_in_hours_new, total_working_on_interview_in_hours_new, total_leave_in_hours, system_hours_worked, total_half_day_leave_in_hours  = get_matrices(combined_log_data_original, device_name)

                    total_hours_worked_without_breaks_new = total_hours_worked_new - total_break_in_hours_new - total_working_on_other_system_in_hours_new - total_system_down_in_hours_new - total_half_day_leave_in_hours


                    # Calculate hours per day worked (guard against division by zero)
                    if total_days - leave_days == 0:
                        hours_per_day = 0  # Set to 0 or handle the case as needed
                    else:
                        hours_per_day = total_hours_worked_without_breaks_new / (total_days - leave_days)

                    # Calculate expected hours worked
                    expected_hours = required_hours * (total_days - leave_days)
                    # Convert 'Total Time (hours)' to minutes
                    results['Total Time (minutes)'] = results['Total Time (hours)'] * 60
                    
                    # Convert to hours and minutes format
                    results['Formatted Time'] = results['Total Time (hours)'].apply(hours_to_hours_minutes)

                    st.markdown(
                        """
                    <style>
                    button {
                        opacity: 1 !important;
                        transform: scale(1) !important;
                    }
                    </style>
                    """,
                        unsafe_allow_html=True,
                    )
                    # Main content - organize into tabs
                    tab1, tab2 = st.tabs(["Analytics", "Image Viewer"])
                    with tab1:
                        
                        formatted_total_hours_worked_without_breaks = hours_to_hours_minutes(total_hours_worked_without_breaks_new)
                        formatted_expected_hours = hours_to_hours_minutes(expected_hours)
                        formatted_total_break_time = hours_to_hours_minutes(total_break_in_hours_new)
                        formatted_hours_per_day = hours_to_hours_minutes(hours_per_day)

                        # Difference between total hours worked without breaks and expected hours
                        difference = total_hours_worked_without_breaks_new - expected_hours
                        formatted_difference = hours_to_hours_minutes(abs(difference))  # Use absolute value for positive display

                        
                        # Display the KPIs in a grid layout with borders
                        with st.expander("Work Hours Calculator", expanded=True):
                            st.markdown("""
                                    <style>
                                    .kpi-box {
                                        border: 1px solid #444; /* Visible border for each KPI box */
                                        padding: 15px;
                                        border-radius: 8px;
                                        margin-bottom: 10px;
                                        text-align: center;
                                        background-color: #1e1f21; /* Dark background for a professional look */
                                        color: #E0E0E0; /* Light text color for readability */
                                    }
                                    .kpi-label {
                                        font-size: 16px;
                                        font-weight: bold;
                                        color: #d9d9d9;
                                    }
                                    .kpi-value {
                                        font-size: 25px;
                                        font-weight: bold;
                                        color: #2171c2; /* Bright cyan for the KPI values */
                                    }
                                    .leave-details {
                                        text-align: left; /* Align leave details to the left for better readability */
                                        padding: 10px;
                                        background-color: #2a2b2e; /* Slightly different background for the leave details section */
                                        border-radius: 5px;
                                        height: 150px; /* Fixed height for the leave dates section */
                                        overflow-y: auto; /* Enable vertical scrolling if content exceeds height */
                                    }
                                    .leave-item {
                                        padding: 5px;
                                        margin-bottom: 5px;
                                        background-color: #333; /* Darker background for each leave item */
                                        border-radius: 3px;
                                        color: #ffffff; /* White text color for leave items */
                                    }
                                    </style>
                                """, unsafe_allow_html=True)
                        
                            col1, col2 = st.columns(2)  # Create two columns for the grid layout

                            with col1:
                                # Display KPIs in the second column
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Average Hours/Day</div>
                                        <div class="kpi-value">{formatted_hours_per_day}</div>
                                    </div>
                                """, unsafe_allow_html=True)
                                
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Expected Hours</div>
                                        <div class="kpi-value">{formatted_expected_hours}</div>
                                    </div>
                                """, unsafe_allow_html=True)
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Total Break Time</div>
                                        <div class="kpi-value">{formatted_total_break_time}</div>
                                    </div>
                                """, unsafe_allow_html=True)

                                
                                # Display Total Leave Days
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Total Leave Days</div>
                                        <div class="kpi-value">{leave_days} days</div>
                                    </div>
                                """, unsafe_allow_html=True)
                                    

                            with col2:
                                
                                # Display KPIs in the first column
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Total Days</div>
                                        <div class="kpi-value">{total_days} days</div>
                                    </div>
                                """, unsafe_allow_html=True)
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Total Hours Worked (without breaks)</div>
                                        <div class="kpi-value">{formatted_total_hours_worked_without_breaks}</div>
                                    </div>
                                """, unsafe_allow_html=True)
                                # Determine the color based on whether the difference is positive or negative
                                if difference > 0:
                                    difference_color = "green"  # Positive difference
                                    formatted_difference_with_sign = f"+{formatted_difference}"  # Add "+" sign for positive numbers
                                elif difference < 0:
                                    difference_color = "red"  # Negative difference
                                    formatted_difference_with_sign = f"-{formatted_difference}"  # Negative sign already handled
                                else:
                                    difference_color = "gray"  # Neutral color for zero difference
                                    formatted_difference_with_sign = formatted_difference  # No sign for zero

                                # Display the difference metric with the color formatting and sign
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Difference</div>
                                        <div class="kpi-value" style="color: {difference_color};">{formatted_difference_with_sign}</div>
                                    </div>
                                """, unsafe_allow_html=True)

                                # Display Total Leave Days
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Extra working days</div>
                                        <div class="kpi-value">{extra_days} days</div>
                                    </div>
                                """, unsafe_allow_html=True)
                                


                            # Conditionally display the leave dates only if there are leave days
                            if leave_days > 0:
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Leave Dates</div>
                                        <div class="leave-details">
                                            {"".join([f'<div class="leave-item">{date}</div>' for date in lsLeaveDates])}
                                        </div>
                                    </div>
                                """, unsafe_allow_html=True)

                            if extra_days > 0:
                                st.markdown(f"""
                                    <div class="kpi-box">
                                        <div class="kpi-label">Extra Working Dates</div>
                                        <div class="leave-details">
                                            {"".join([f'<div class="leave-item">{date}</div>' for date in lsExtraWorkDays])}
                                        </div>
                                    </div>
                                """, unsafe_allow_html=True)    
                    
                        # with colomn1:
                        results_rename = results.rename(columns={'WindowTitle': 'WindowTitle(Task)', 'strDate': 'Date'})
                        
                        # Apply the time formatting function to 'Total Time (minutes)'
                        results_rename['Total Time (minutes)'] = results_rename['Total Time (minutes)'].apply(lambda x: hours_to_hours_minutes_seconds(x / 60))

                        color_apps_palette = {}  
                        unique_projects = results['Project'].unique()
                        color_projects_palette = {
                                                        proj.title(): get_application_color(proj, len(unique_projects)) 
                                                        for proj in unique_projects 
                                                    } 
                        unique_customers = results['Customer'].unique()  
                        color_customer_palette = {
                                                        cust.title(): get_application_color(cust, len(unique_customers)) 
                                                        for cust in unique_customers
                                                    }
                        
                        # Standardize 'Application' to lowercase
                        results['Application'] = results['Application'].str.lower()

                        # Group by application and calculate total time spent per application
                        app_usage = results.groupby("Application")["Total Time (hours)"].sum().reset_index()
                        app_usage = app_usage[app_usage['Total Time (hours)'] * 60 >= selected_threshold_minutes] # Remove data that do not meet minimum threshold

                        # Apply the hours to hours and minutes formatting
                        app_usage['Formatted Time'] = app_usage['Total Time (hours)'].apply(hours_to_hours_minutes)

                        # Optionally, convert 'Application' back to title case for display
                        app_usage['Application'] = app_usage['Application'].str.title()

                        # Create a truncated version for the legend
                        app_usage['Truncated Application'] = app_usage['Application'].apply(monHelper.truncate_label)

                        # Generate dynamic colors for applications using the get_application_color function
                        unique_apps = app_usage['Application'].unique()
                        color_apps_palette = {app: get_application_color(app, len(unique_apps)) for app in unique_apps}
                        # print("Top level:",color_projects_palette)
                                    
                        # with st.expander("Time Spent on Different Applications and Projects (in hours)", expanded=True):
                        #     # Ensure 'Total Time (hours)' is numeric
                        #     results['Total Time (hours)'] = pd.to_numeric(results['Total Time (hours)'], errors='coerce').fillna(0)

                        #     # Fill NaN values for categorical columns and replace empty strings with placeholders
                            
                        #     results['Customer'] = results['Customer'].replace({'': 'Not specified', np.nan: 'Not specified'})
                        #     results['Project'] = results['Project'].replace({'': 'Other', np.nan: 'Other'})
                        #     results['Application'] = results['Application'].replace({'': 'Not specified', np.nan: 'Not specified'})
                            
                            
                        #     # Apply filtering for 'Application' column
                        #     if 'Application' in drop_conditions:
                        #         results = results[~results['Application'].isin(drop_conditions['Application'])]

                        #     # Group by 'Application', 'Project', and 'Sub-Project' and sum 'Total Time (hours)'
                        #     sunburst_data = results.groupby(['Customer', 'Project', 'Application'])['Total Time (hours)'].sum().reset_index()

                        #     # Filter out rows where 'Total Time (hours)' is zero
                        #     sunburst_data = sunburst_data[sunburst_data['Total Time (hours)'] > 0]
                        #     sunburst_data = sunburst_data[sunburst_data['Total Time (hours)'] * 60 >= selected_threshold_minutes]

                        #     # Apply the hours to hours and minutes formatting
                        #     sunburst_data['Formatted Time'] = sunburst_data['Total Time (hours)'].apply(hours_to_hours_minutes)

                        #     # Use the predefined color palettes for applications and projects
                        #     color_sunburst_palette = {
                        #         **color_customer_palette,
                        #         **color_projects_palette, 
                        #         **color_apps_palette,  # Use the same palette for applications
                                
                                
                        #         # Use the same palette for projects
                        #         # **{sub_proj: get_application_color(sub_proj, len(unique_sub_projects)) for sub_proj in unique_sub_projects}  # New colors for sub-projects
                        #     }

                        #     # Ensure there is data to plot
                        #     try:
                                
                        #         if not sunburst_data.empty and sunburst_data['Total Time (hours)'].sum() > 0:
                        #             # Create the sunburst chart with Plotly
                        #             sunburst_fig = px.sunburst(
                        #                 sunburst_data,
                        #                 path=['Customer', 'Project', 'Application'],
                        #                 values='Total Time (hours)',
                        #                 color='Application',  # Use 'Application' for color mapping
                        #                 color_discrete_map=color_sunburst_palette,  # Apply the predefined color palette
                        #                 width=800,
                        #                 height=800,
                        #                 custom_data=['Formatted Time']  # Add the formatted time to custom data for hover
                        #             )

                        #             # Update the hover template to show formatted time
                        #             sunburst_fig.update_traces(
                        #                 hovertemplate='<b>%{label}</b><br>Total Time: %{customdata[0]}'
                        #             )

                        #             # Display the chart in Streamlit
                        #             st.plotly_chart(sunburst_fig, use_container_width=True)
                        #         else:
                        #             st.write("No valid data available for the sunburst chart.")
                        #     except Exception as e:
                        #         import traceback
                        #         print(traceback.print_exc())
                        

                        # Within the Analytics tab, replace the original sunburst chart code
                                                
                        with st.expander("Time Spent on Different Applications and Projects (in hours)", expanded=True):
                            # Create a copy of combined_log_data_original to avoid modifying the original
                            sunburst_df = combined_log_data_original.copy()

                            # Validate and convert TimeSpentActual (seconds) to Total Time (hours)
                            sunburst_df['TimeSpentActual'] = pd.to_numeric(sunburst_df['TimeSpentActual'], errors='coerce').fillna(0)
                            sunburst_df = sunburst_df[sunburst_df['TimeSpentActual'] >= 0]  # Exclude negative values
                            sunburst_df['Total Time (hours)'] = sunburst_df['TimeSpentActual'] / 3600

                            # Apply threshold filter before grouping (convert threshold to hours)
                            sunburst_df = sunburst_df[sunburst_df['Total Time (hours)'] * 60 >= selected_threshold_minutes]

                            # Ensure categorical columns exist and fill NaN/empty values
                            # Standardize Customer to title case to resolve case sensitivity (e.g., "Real" vs. "REAL")
                            sunburst_df['Customer'] = sunburst_df.get('Customer', 'Not specified').replace({'': 'Not specified', np.nan: 'Not specified'}).str.title()
                            sunburst_df['Project'] = sunburst_df.get('Project', 'Other').replace({'': 'Other', np.nan: 'Other'})
                            sunburst_df['Application'] = sunburst_df.get('Application', 'Not specified').replace({'': 'Not specified', np.nan: 'Not specified'})

                            # Standardize 'Application' to lowercase for consistency
                            sunburst_df['Application'] = sunburst_df['Application'].str.lower()

                            # For device-specific analysis, filter by PCName if device_name is set
                            if device_name:
                                sunburst_df = sunburst_df[sunburst_df['PCName'] == device_name]

                            # Prepare data for go.Sunburst by creating entries for each level
                            # Level 1: Customer
                            customer_df = sunburst_df.groupby('Customer')['Total Time (hours)'].sum().reset_index()
                            customer_df['Formatted Time'] = customer_df['Total Time (hours)'].apply(hours_to_hours_minutes)
                            customer_df['ID'] = customer_df['Customer']
                            customer_df['Parent'] = ''
                            customer_df['Label'] = customer_df['Customer']
                            customer_df['Level'] = 'Customer'
                            customer_df['Hover Time'] = customer_df['Formatted Time']

                            # Level 2: Project
                            project_df = sunburst_df.groupby(['Customer', 'Project'])['Total Time (hours)'].sum().reset_index()
                            project_df['Formatted Time'] = project_df['Total Time (hours)'].apply(hours_to_hours_minutes)
                            project_df['ID'] = project_df['Customer'] + '|' + project_df['Project']
                            project_df['Parent'] = project_df['Customer']
                            project_df['Label'] = project_df['Project']
                            project_df['Level'] = 'Project'
                            project_df['Hover Time'] = project_df['Formatted Time']

                            # Level 3: Application
                            app_df = sunburst_df.groupby(['Customer', 'Project', 'Application'])['Total Time (hours)'].sum().reset_index()
                            app_df['Formatted Time'] = app_df['Total Time (hours)'].apply(hours_to_hours_minutes)
                            app_df['ID'] = app_df['Customer'] + '|' + app_df['Project'] + '|' + app_df['Application']
                            app_df['Parent'] = app_df['Customer'] + '|' + app_df['Project']
                            app_df['Label'] = app_df['Application']
                            app_df['Level'] = 'Application'
                            app_df['Hover Time'] = app_df['Formatted Time']

                            # Concatenate all levels into a single DataFrame
                            sunburst_data = pd.concat([customer_df, project_df, app_df], ignore_index=True)
                            sunburst_data = sunburst_data[sunburst_data['Total Time (hours)'] > 0]  # Filter out zero time entries

                            # Generate color palettes
                            unique_projects = sunburst_df['Project'].unique()
                            color_projects_palette = {
                                proj.title(): get_application_color(proj, len(unique_projects))
                                for proj in unique_projects
                            }
                            unique_customers = sunburst_df['Customer'].unique()
                            color_customer_palette = {
                                cust.title(): get_application_color(cust, len(unique_customers))
                                for cust in unique_customers
                            }
                            unique_apps = app_df['Application'].unique()
                            color_apps_palette = {
                                app: get_application_color(app, len(unique_apps))
                                for app in unique_apps
                            }

                            # Combine color palettes for the sunburst chart
                            color_sunburst_palette = {
                                **color_customer_palette,
                                **color_projects_palette,
                                **color_apps_palette
                            }

                            # Map colors based on level and label
                            def get_color(row):
                                if row['Level'] == 'Customer':
                                    return color_customer_palette.get(row['Label'], '#D3D3D3')  # Light gray fallback
                                elif row['Level'] == 'Project':
                                    return color_projects_palette.get(row['Label'].title(), '#A9A9A9')  # Dark gray fallback
                                else:  # Application
                                    return color_apps_palette.get(row['Label'], '#696969')  # Dim gray fallback

                            sunburst_data['Color'] = sunburst_data.apply(get_color, axis=1)

                            # Ensure there is data to plot
                            try:
                                if not sunburst_data.empty and sunburst_data['Total Time (hours)'].sum() > 0:
                                    # Create the sunburst chart with go.Sunburst
                                    sunburst_fig = go.Figure(go.Sunburst(
                                        labels=sunburst_data['Label'],
                                        parents=sunburst_data['Parent'],
                                        values=sunburst_data['Total Time (hours)'],
                                        ids=sunburst_data['ID'],
                                        marker=dict(colors=sunburst_data['Color']),
                                        customdata=sunburst_data[['Hover Time', 'Level']],
                                        hovertemplate='<b>%{label}</b><br>%{customdata[1]} Total Time: %{customdata[0]}<extra></extra>',
                                        branchvalues='total',  # Ensure parent values are sums of children
                                        insidetextorientation='radial'
                                    ))

                                    # Update layout
                                    sunburst_fig.update_layout(
                                        width=800,
                                        height=800
                                    )

                                    # Display the chart in Streamlit
                                    st.plotly_chart(sunburst_fig, use_container_width=True)
                                else:
                                    st.write("No valid data available for the sunburst chart.")
                            except Exception as e:
                                import traceback
                                print(traceback.print_exc())
                                st.write("Error rendering the sunburst chart.")


                        col1, col2 = st.columns(2)
                        
                        with col1:
                            
                                # results['Customer'] = results['Customer'].str.lower()
                            # unique_customers = results['Customer'].unique()
                            # color_customer_palette = {
                            #                             cust.title(): get_application_color(cust, len(unique_customers)) 
                            #                             for cust in unique_customers
                            #                         }

                            with st.expander("Customer Distribution", expanded=True):
                                # Standardize 'Customer' to lowercase
                                results['Customer'] = results['Customer'].str.lower()

                                # Group by customer and calculate total time spent per customer
                                customer_usage = results.groupby("Customer")["Total Time (hours)"].sum().reset_index()
                                customer_usage = customer_usage[customer_usage['Total Time (hours)'] * 60 >= selected_threshold_minutes] # Remove data that do not meet minimum threshold

                                # Standardize customer names and apply a fallback for empty names
                                customer_usage["Customer"] = customer_usage["Customer"].apply(lambda x: x.title() if x else "Other Customers")

                                # Truncate customer names for the legend
                                customer_usage['Truncated Customer'] = customer_usage['Customer'].apply(monHelper.truncate_label)

                                # Apply hours to hours and minutes formatting
                                customer_usage['Formatted Time'] = customer_usage['Total Time (hours)'].apply(hours_to_hours_minutes)

                                # Generate dynamic colors for customers using the get_application_color function
                                # unique_customers = customer_usage['Customer'].unique()
                                # color_customer_palette = {cust: get_application_color(cust, len(unique_customers)) for cust in unique_customers}

                                # Create a pie chart using Plotly
                                fig = px.pie(
                                        customer_usage, 
                                        names='Truncated Customer', 
                                        values='Total Time (hours)',
                                        color='Customer',
                                        color_discrete_map=customer_palette,  # Use shared palette
                                        custom_data=['Customer', 'Formatted Time'],
                                        hole=0.4
                                    )


                                # Update the traces to show formatted time and percentage in the chart and full customer name on hover
                                fig.update_traces(
                                    textposition='inside', 
                                    textinfo='percent+label', 
                                    hovertemplate='Customer: %{customdata[0][0]}<br>Total Time: %{customdata[0][1]}<br>Percent: %{percent}',
                                    texttemplate='%{customdata[1]}<br>(%{percent})'
                                )

                                # Update layout to improve text formatting
                                fig.update_layout(uniformtext_minsize=12, uniformtext_mode='hide')

                                # Display the chart in the Streamlit app
                                st.plotly_chart(fig, use_container_width=True)
                                
                            #     unique_projects = results['Project'].unique()
                            # color_projects_palette = {
                            #                             proj.title(): get_application_color(proj, len(unique_projects)) 
                            #                             for proj in unique_projects
                                                    # }
                            with st.expander("Project Distribution", expanded=True):
                                # Standardize 'Project' to lowercase
                                results['Project'] = results['Project'].str.lower()

                                # Group by project and calculate the total time spent per project
                                project_usage = results.groupby("Project")["Total Time (hours)"].sum().reset_index()
                                project_usage = project_usage[project_usage['Total Time (hours)'] * 60 >= selected_threshold_minutes] # Remove data that do not meet minimum threshold

                                # Standardize project names and apply a fallback for empty names
                                project_usage["Project"] = project_usage["Project"].apply(lambda x: x.title() if x else "Other Projects")

                                # Truncate project names for the legend
                                project_usage['Truncated Project'] = project_usage['Project'].apply(monHelper.truncate_label)

                                # Apply hours to hours and minutes formatting
                                project_usage['Formatted Time'] = project_usage['Total Time (hours)'].apply(hours_to_hours_minutes)

                                # Generate dynamic colors for projects using the get_application_color function
                                # unique_projects = project_usage['Project'].unique()
                                # color_projects_palette = {proj: get_application_color(proj, len(unique_projects)) for proj in unique_projects}

                                # Create a pie chart using Plotly
                                fig = px.pie(
                                    project_usage, 
                                    values='Total Time (hours)', 
                                    names='Truncated Project',
                                    color='Project',
                                    color_discrete_map=project_palette,  # Use shared palette
                                    custom_data=['Project', 'Formatted Time'],
                                    hole=0.4
                                )


                                # Update the traces to show formatted time and percentage in the chart and full project name on hover
                                fig.update_traces(
                                    textposition='inside', 
                                    textinfo='percent+label', 
                                    hovertemplate='Project: %{customdata[0][0]}<br>Total Time: %{customdata[0][1]}<br>Percent: %{percent}',
                                    texttemplate='%{customdata[1]}<br>(%{percent})'
                                )

                                # Update layout to improve text formatting
                                fig.update_layout(uniformtext_minsize=12, uniformtext_mode='hide')

                                # Display the chart in the Streamlit app
                                st.plotly_chart(fig, use_container_width=True)

                            with st.expander("Application Usage Chart", expanded=True):
                                # # Standardize 'Application' to lowercase
                                # results['Application'] = results['Application'].str.lower()

                                # # Group by application and calculate total time spent per application
                                # app_usage = results.groupby("Application")["Total Time (hours)"].sum().reset_index()
                                # app_usage = app_usage[app_usage['Total Time (hours)'] * 60 >= selected_threshold_minutes] # Remove data that do not meet minimum threshold

                                # # Apply the hours to hours and minutes formatting
                                # app_usage['Formatted Time'] = app_usage['Total Time (hours)'].apply(hours_to_hours_minutes)

                                # # Optionally, convert 'Application' back to title case for display
                                # app_usage['Application'] = app_usage['Application'].str.title()

                                # # Create a truncated version for the legend
                                # app_usage['Truncated Application'] = app_usage['Application'].apply(monHelper.truncate_label)

                                # # Generate dynamic colors for applications using the get_application_color function
                                # unique_apps = app_usage['Application'].unique()
                                # color_apps_palette = {app: get_application_color(app, len(unique_apps)) for app in unique_apps}

                                # Create a pie chart using Plotly
                                fig = px.pie(
                                    app_usage, 
                                    values='Total Time (hours)', 
                                    names='Truncated Application',
                                    color='Application',  # Apply the dynamic colors
                                    color_discrete_map=application_palette,  # Use the dynamic color mapping
                                    custom_data=['Application', 'Formatted Time'],  # Include full application name and formatted time for hover
                                    hole=0.4  # Donut chart
                                )

                                # Update the traces to show formatted time and percentage in the chart and full application name on hover
                                fig.update_traces(
                                    textposition='inside', 
                                    textinfo='percent+label',
                                    hovertemplate='Application: %{customdata[0][0]}<br>Total Time: %{customdata[0][1]}<br>Percent: %{percent}',
                                    texttemplate='%{customdata[1]}<br>(%{percent})'
                                )

                                # Display the chart in the Streamlit app
                                st.plotly_chart(fig, use_container_width=True)


                                # Create a pie chart for device usage
                            with st.expander("Device Usage Chart", expanded=True):
                                if allDeviceResults is not None and not allDeviceResults.empty:
                                    # Filter data based on the current device if set
                                    current_device_data = allDeviceResults if not device_name else allDeviceResults[allDeviceResults['Device Name'] == device_name]

                                    # Drop rows with missing 'Device Name'
                                    current_device_data = current_device_data.dropna(subset=['Device Name'])

                                    # Rename columns for consistency (if needed)
                                    device_results_rename = current_device_data.rename(columns={'Device Name': 'Device Name'})

                                    # Apply the time formatting function to 'Total Time (minutes)' if it exists, otherwise use 'Total Time (hours)'
                                    if 'Total Time (minutes)' in device_results_rename.columns:
                                        device_results_rename['Total Time (minutes)'] = device_results_rename['Total Time (minutes)'].apply(lambda x: hours_to_hours_minutes_seconds(x / 60))
                                    else:
                                        device_results_rename['Total Time (hours)'] = device_results_rename['Total Time (hours)']  # Ensure 'Total Time (hours)' is present

                                    # Group by device and calculate total time spent per device
                                    device_usage = device_results_rename.groupby("Device Name")["Total Time (hours)"].sum().reset_index()
                                    device_usage = device_usage[device_usage['Total Time (hours)'] * 60 >= selected_threshold_minutes]  # Apply the same threshold

                                    # Apply the hours to hours and minutes formatting
                                    device_usage['Formatted Time'] = device_usage['Total Time (hours)'].apply(hours_to_hours_minutes)

                                    # Optionally, convert 'Device Name' to title case for display (if not already)
                                    device_usage['Device Name'] = device_usage['Device Name'].str.title()

                                    # Create a truncated version for the legend
                                    device_usage['Truncated Device Name'] = device_usage['Device Name'].apply(monHelper.truncate_label)

                                    # Generate dynamic colors for devices using the get_application_color function
                                    unique_devices = device_usage['Device Name'].unique()
                                    color_device_palette = {device: get_application_color(device, len(unique_devices)) for device in unique_devices}

                                    # Create a pie chart using Plotly
                                    fig = px.pie(
                                        device_usage,
                                        values='Total Time (hours)',
                                        names='Truncated Device Name',
                                        color='Device Name',  # Apply the dynamic colors
                                        color_discrete_map=color_device_palette,  # Use the dynamic color mapping
                                        custom_data=['Device Name', 'Formatted Time'],  # Include full device name and formatted time for hover
                                        hole=0.4  # Donut chart
                                    )

                                    # Update the traces to show formatted time and percentage in the chart and full device name on hover
                                    fig.update_traces(
                                        textposition='inside',
                                        textinfo='percent+label',
                                        hovertemplate='Device: %{customdata[0]}<br>Total Time: %{customdata[1]}<br>Percent: %{percent}',
                                        texttemplate='%{customdata[1]}<br>(%{percent})'
                                    )

                                    # Update layout to improve text formatting
                                    fig.update_layout(uniformtext_minsize=12, uniformtext_mode='hide')

                                    # Display the chart in the Streamlit app
                                    st.plotly_chart(fig, use_container_width=True)
                                else:
                                    st.write("No device usage data available.")
                                    
                        with col2:
                            with st.expander("Customer Hours Distribution", expanded=True):
                                # Group by customer and calculate total time spent per customer
                                customer_hours = results.groupby('Customer')["Total Time (hours)"].sum().reset_index()
                                customer_hours = customer_hours[customer_hours['Total Time (hours)'] * 60 >= selected_threshold_minutes] # Remove data that do not meet minimum threshold

                                # Replace empty customer names with "Other Customers" and format names to title case
                                customer_hours['Customer'] = customer_hours['Customer'].apply(lambda x: x if x != '' else 'Other Customers')
                                customer_hours['Customer'] = customer_hours['Customer'].str.title()

                                # Apply hours to hours and minutes formatting
                                customer_hours['Formatted Time'] = customer_hours['Total Time (hours)'].apply(hours_to_hours_minutes)

                                # Create a bar chart using Plotly
                                fig = px.bar(
                                            customer_hours, 
                                            x='Customer', 
                                            y='Total Time (hours)', 
                                            text='Formatted Time',
                                            color='Customer',
                                            color_discrete_map=customer_palette  # Use shared palette
                                        )


                                # Update traces to show formatted time outside the bars
                                fig.update_traces(  texttemplate='%{text}',
                                                    textposition='outside',
                                                    hovertemplate='<b>Application:</b> %{fullData.name}<br><b>Time:</b> %{text}<extra></extra>')

                                # Update layout to improve text formatting
                                fig.update_layout(
                                    uniformtext_minsize=12  # Ensure the text is fully shown
                                )

                                # Display the chart in the Streamlit app
                                st.plotly_chart(fig, use_container_width=True)
                            

                            with st.expander("Project Hours Distribution", expanded=True):
            
                                # Group by project and calculate total time spent per project
                                project_hours = results.groupby('Project')["Total Time (hours)"].sum().reset_index()
                                project_hours = project_hours[project_hours['Total Time (hours)'] * 60 >= selected_threshold_minutes] # Remove data that do not meet minimum threshold

                                # Replace empty project names with "Other Projects" and format project names to title case
                                project_hours['Project'] = project_hours['Project'].apply(lambda x: x if x != '' else 'Other Projects')
                                project_hours['Project'] = project_hours['Project'].str.title()

                                # Apply hours to hours and minutes formatting
                                project_hours['Formatted Time'] = project_hours['Total Time (hours)'].apply(hours_to_hours_minutes)

                                # Create a bar chart using Plotly
                                fig = px.bar(
                                        project_hours, 
                                        x='Project', 
                                        y='Total Time (hours)', 
                                        text='Formatted Time',
                                        color='Project',
                                        color_discrete_map=project_palette  # Use shared palette
                                    )

                                # Update traces to show formatted time outside the bars
                                fig.update_traces(  texttemplate='%{text}', 
                                                    textposition='outside', 
                                                    hovertemplate='<b>Application:</b> %{fullData.name}<br><b>Time:</b> %{text}<extra></extra>')

                                # Add layout adjustments to match the previous style
                                fig.update_layout(
                                    uniformtext_minsize=12,  # Adjust the minimum size of the text
                                    uniformtext_mode='show',  # Ensure the text is fully shown
                                )

                                # Display the chart in the Streamlit app
                                st.plotly_chart(fig, use_container_width=True)
                            
                            
                                # Incorporating leave logic into the chart
                            with st.expander("Daily Application Hours Distribution", expanded=True):
                                # Convert 'strDate' to datetime

                                resultsUpdated['Time'] = pd.to_datetime(resultsUpdated['Time'], errors='coerce')
                                if not pd.api.types.is_datetime64_any_dtype(resultsUpdated['Time']):
                                    st.write("The 'Time' column could not be converted to datetime format.")
                                else:
                                    resultsUpdated['DateDay']   = resultsUpdated['Time'].dt.strftime('%Y-%m-%d (%a)')
                                    # resultsUpdated['Application'] = resultsUpdated['Application'].str.title()

                                    # 2) Aggregate
                                    app_hours_per_day = (
                                        resultsUpdated
                                        .groupby(['DateDay','Application'], as_index=False)
                                        .agg({
                                            'TimeSpent_hr':    'sum',
                                            'TimeSpentActual':'sum',
                                            'IsRemapped':      'min',
                                        })
                                    )
                                    app_hours_per_day['ParentApplication'] = (
                                        app_hours_per_day['Application']
                                        .apply(get_parent_application)
                                    )

                                    # 3) Threshold, dates & merging
                                    app_hours_per_day = app_hours_per_day[
                                        app_hours_per_day['TimeSpent_hr'] * 60 >= selected_threshold_minutes
                                    ]
                                    all_dates = pd.date_range(start_date, end_date, freq='B') \
                                                    .strftime('%Y-%m-%d (%a)').tolist()
                                    all_dates = sorted(set(all_dates) | set(app_hours_per_day['DateDay']))
                                    weekends_with_work = resultsUpdated[
                                        resultsUpdated['Time'].dt.weekday >= 5
                                    ]['DateDay'].unique().tolist()
                                    all_dates = sorted(set(all_dates) | set(weekends_with_work))

                                    all_dates_df = pd.DataFrame({'DateDay': all_dates})
                                    app_hours_per_day = pd.merge(all_dates_df, app_hours_per_day,
                                                                on='DateDay', how='left')
                                    # fill numeric, leave cats to next step
                                    for col in ['TimeSpent_hr','TimeSpentActual','IsRemapped']:
                                        app_hours_per_day[col].fillna(0, inplace=True)

                                    # 4) Ensure 'Leave' category exists, then mark leave days
                                    if not pd.api.types.is_categorical_dtype(app_hours_per_day['Application']):
                                        app_hours_per_day['Application'] = app_hours_per_day['Application'].astype('category')
                                    app_hours_per_day['Application'] = (
                                        app_hours_per_day['Application']
                                        .cat.add_categories(['Leave'])
                                    )
                                    app_hours_per_day.loc[
                                        app_hours_per_day['DateDay'].isin(lsLeaveDates),
                                        ['Application','TimeSpent_hr']
                                    ] = ['Leave', 0]

                                    # 5) Final sorting and formatting
                                    app_hours_per_day['DateDay'] = pd.Categorical(
                                        app_hours_per_day['DateDay'],
                                        categories=all_dates,
                                        ordered=True
                                    )
                                    app_hours_per_day['Formatted Time'] = (
                                        app_hours_per_day['TimeSpent_hr']
                                        .apply(hours_to_hours_minutes)
                                    )

                                    specials     = ["Break", "Desktop"]
                                    parents      = [p for p in app_hours_per_day['ParentApplication'].unique() if p not in specials]
                                    parent_order = parents + specials   # note: specials *last*

                                    child_groups = defaultdict(list)
                                    for app in app_hours_per_day['Application'].cat.categories:
                                        parent_groups = get_parent_application(app)
                                        child_groups[parent_groups].append(app)

                                    ordered_apps = []
                                    for p in parent_order:
                                        # sort each subgroup if you like
                                        ordered_apps.extend(sorted(child_groups.get(p, [])))

                                    # 7) Load palettes + build remapped shapes
                                    base_color_map      = monHelper.ReadJSON("Sourcecode/color_palette.json")["application"]
                                    raw_remapped  = monHelper.ReadJSON("Sourcecode/color_palette.json")["remapped"]
                                    color_map_final    = get_extended_color_map(app_hours_per_day, base_color_map)

                                    available_shapes = ["/","\\","-","|","."]
                                    remapped_shapes  = {}
                                    fig = go.Figure()
                                    for app in ordered_apps:
                                        bar_color  = color_map_final.get(app)
                                        hatch_color = raw_remapped.get(app, "gray")
                                        subdf = app_hours_per_day[app_hours_per_day['Application'] == app]
                                        is_remapped = bool(subdf['IsRemapped'].iloc[0])
                                        parent      = subdf['ParentApplication'].iloc[0]
                                                # 4) Decide if & how to hatch
                                        pattern = None
                                        if is_remapped:
                                            # remapped apps: cycle through shapes, color from remap_pal
                                            remapped_shapes.setdefault(parent, itertools.cycle(available_shapes))
                                            shape = next(remapped_shapes[parent])
                                            pattern = dict(
                                                shape   = shape,
                                                fgcolor = hatch_color,
                                                bgcolor = bar_color,
                                                fillmode= "overlay",
                                                solidity= 0.3
                                            )
                                        elif app.startswith("Meeting_"):
                                            pattern = dict(
                                                shape   = "x",
                                                fgcolor = 'gray',
                                                bgcolor = bar_color,
                                                fillmode= "overlay",
                                                solidity= 0.3
                                            )
                                        elif app in ["Break","Desktop"]:
                                            pattern = dict(
                                                shape   = ".",
                                                fgcolor = 'gray',
                                                bgcolor = bar_color,
                                                fillmode= "overlay",
                                                solidity= 0.3
                                            )
                                        # else pattern stays None → pure solid

                                        # 5) Build marker dict
                                        marker_kwargs = {"color": color_map_final.get(app)}
                                        if pattern:
                                            marker_kwargs["pattern"] = pattern
                                        fig.add_trace(go.Bar(
                                            x=subdf['DateDay'],
                                            y=subdf['TimeSpent_hr'],
                                            name=app,
                                            text=subdf['Formatted Time'],
                                            textposition='inside',
                                            hovertemplate=(
                                                '<b>App:</b> %{fullData.name}<br>'
                                                '<b>Date:</b> %{x}<br>'
                                                '<b>Time:</b> %{text}<extra></extra>'
                                            ),
                                            marker=marker_kwargs
                                        ))

                                    # 9) Add 8.5h line + annotations
                                    fig.add_shape(dict(
                                        type='line', x0=0, x1=1, y0=8.5, y1=8.5,
                                        xref='paper', yref='y',
                                        line=dict(dash='dashdot', color='yellow')
                                    ))
                                    total_hours = (
                                        app_hours_per_day
                                        .groupby('DateDay', as_index=False)['TimeSpent_hr']
                                        .sum()
                                    )
                                    total_hours['Formatted'] = total_hours['TimeSpent_hr'] \
                                                                .apply(hours_to_hours_minutes)
                                    for _, row in total_hours.iterrows():
                                        fig.add_annotation(
                                            x=row['DateDay'], y=row['TimeSpent_hr'],
                                            text=row['Formatted'], showarrow=False, yshift=10
                                        )
                                    fig.add_annotation(
                                        x=0.5, y=8.5, text="Total time worked",
                                        showarrow=True, arrowhead=2, ax=0, ay=-40,
                                        font=dict(color='yellow'), align='center', arrowcolor='yellow'
                                    )

                                    # 10) Layout tweaks & render
                                    fig.update_traces(
                                        texttemplate='%{text}', textposition='inside',
                                        hovertemplate=(
                                            '<b>App:</b> %{fullData.name}<br>'
                                            '<b>Date:</b> %{x}<br>'
                                            '<b>Time:</b> %{text}<extra></extra>'
                                        )
                                    )
                                    fig.update_layout(
                                        xaxis=dict(
                                            categoryorder='array',
                                            categoryarray=all_dates
                                        ),
                                        barmode='stack',
                                        uniformtext_minsize=12,
                                        uniformtext_mode='hide',
                                        showlegend=False
                                    )
                                    st.plotly_chart(fig, use_container_width=True)


                            with st.expander("Meeting Chart", expanded=True):
                                if allDeviceResults is not None and not allDeviceResults.empty:
                                    # Filter data based on the current device if set
                                    current_device_data = allDeviceResults if not device_name else allDeviceResults[allDeviceResults['Device Name'] == device_name]

                                    # Filter only meeting-related data (case-insensitive search)
                                    meeting_df = current_device_data[current_device_data['Application'].str.contains("meeting_", case=False, na=False)]

                                    # Ensure there is valid data
                                    if not meeting_df.empty:
                                        # Aggregate meeting time
                                        meeting_usage = meeting_df.groupby("Application")["Total Time (hours)"].sum().reset_index()
                                        meeting_usage = meeting_usage[meeting_usage['Total Time (hours)'] * 60 >= selected_threshold_minutes]  # Apply threshold filter

                                        # Format total time (convert hours into "Xh Ym")
                                        meeting_usage['Formatted Time'] = meeting_usage['Total Time (hours)'].apply(lambda x: f"{int(x)}h {int((x % 1) * 60)}m")

                                        # Optionally, convert 'Application' to title case for display (if not already)
                                        meeting_usage['Application'] = meeting_usage['Application'].str.title()

                                        # Create a truncated version for the legend
                                        meeting_usage['Truncated Application'] = meeting_usage['Application'].apply(monHelper.truncate_label)

                                        # Generate dynamic colors for meeting applications using the get_application_color function
                                        unique_meetings = meeting_usage['Application'].unique()
                                        color_meeting_palette = {meeting: get_application_color(meeting, len(unique_meetings)) for meeting in unique_meetings}

                                        # Create Pie Chart
                                        fig = px.pie(
                                            meeting_usage,
                                            values='Total Time (hours)',
                                            names='Truncated Application',
                                            color='Application',  # Apply the dynamic colors
                                            color_discrete_map=color_meeting_palette,  # Use the dynamic color mapping
                                            custom_data=['Application', 'Formatted Time'],  # Show extra info on hover
                                            hole=0.4  # Donut style
                                        )

                                        # Update hover info
                                        fig.update_traces(
                                            textposition='inside',
                                            textinfo='percent+label',
                                            hovertemplate='Meeting: %{customdata[0]}<br>Total Time: %{customdata[1]}<br>Percent: %{percent}',
                                            texttemplate='%{customdata[1]}<br>(%{percent})'
                                        )

                                        # Update layout to improve text formatting
                                        fig.update_layout(uniformtext_minsize=12, uniformtext_mode='hide')

                                        # Display in Streamlit
                                        st.plotly_chart(fig, use_container_width=True)
                                    else:
                                        st.write("No meeting data available.")
                                else:
                                    st.write("No data available to generate the meeting chart.")

                        with st.expander("Timeline Chart", expanded=True):
                            try:
                                if combined_log_data is not None and not combined_log_data.empty:
                                    # Prepare data similar to plot_actual_usage_chart
                                    timeline_data = combined_log_data.copy()
                                    timeline_data = timeline_data.drop('RealTime', axis=1)
                                    timeline_data['Time'] = pd.to_datetime(timeline_data['Time'], errors='coerce')

                                    # Drop rows with invalid timestamps
                                    timeline_data = timeline_data.dropna(subset=['Time'])

                                    # Rename columns to match plot_actual_usage_chart expectations
                                    timeline_data = timeline_data.rename(columns={
                                        'Time': 'RealTime',
                                        'TotalTime': 'TimeSpentActual',  # Assuming TotalTime is in seconds
                                        'Application': 'Application',
                                        'strDate': 'Date'  # Use date as the y-axis category (replacing UserName)
                                    })

                                    # Filter based on selected_threshold_minutes
                                    timeline_data['DurationHrs'] = timeline_data['TimeSpentActual'] / 3600  # Convert seconds to hours
                                    timeline_data = timeline_data[timeline_data['DurationHrs'] * 60 >= selected_threshold_minutes]  # Apply threshold

                                    # Calculate end time
                                    timeline_data['EndTime'] = timeline_data['RealTime'] + pd.to_timedelta(timeline_data['TimeSpentActual'], unit='s')

                                    # Calculate start and end times in fractional hours
                                    timeline_data['HourFloatStart'] = (
                                        timeline_data['RealTime'].dt.hour +
                                        timeline_data['RealTime'].dt.minute / 60 +
                                        timeline_data['RealTime'].dt.second / 3600
                                    )
                                    timeline_data['HourFloatEnd'] = (
                                        timeline_data['EndTime'].dt.hour +
                                        timeline_data['EndTime'].dt.minute / 60 +
                                        timeline_data['EndTime'].dt.second / 3600
                                    )

                                    # Adjust times to start at 8 AM (times before 8 AM are shifted to the next day)
                                    timeline_data['HourFloatStart_adj'] = timeline_data['HourFloatStart'].apply(lambda x: x if x >= 8 else x + 24)
                                    timeline_data['HourFloatEnd_adj'] = timeline_data['HourFloatEnd'].apply(lambda x: x if x >= 8 else x + 24)
                                    timeline_data['DurationHrs_adj'] = timeline_data['HourFloatEnd_adj'] - timeline_data['HourFloatStart_adj']

                                    # Format the date for y-axis (similar to UserName in the original)
                                    timeline_data['Date'] = timeline_data['RealTime'].dt.strftime('%Y-%m-%d (%a)')

                                    # Filter timeline_data based on device_name if set
                                    if device_name:
                                        timeline_data = timeline_data[timeline_data['PCName'] == device_name]  # Assuming 'PCName' is the device column

                                    # Initialize Plotly figure
                                    fig = go.Figure()

                                    # Load color palette
                                    base_color_map = monHelper.ReadJSON("Sourcecode/color_palette.json")["application"]
                                    dictColorMap = get_extended_color_map(timeline_data, base_color_map)

                                    # Plot each application as a horizontal bar
                                    for app in timeline_data['Application'].unique():
                                        subdf = timeline_data[timeline_data['Application'] == app].copy()
                                        subdf['Start_HHMM'] = subdf['HourFloatStart_adj'].apply(hours_to_hours_minutes_inFormat)
                                        subdf['Duration_HHMM'] = subdf['DurationHrs_adj'].apply(hours_to_minutes_seconds)
                                        customdata = np.stack((subdf['Start_HHMM'], subdf['Duration_HHMM'], [app] * len(subdf)), axis=-1)
                                        color = dictColorMap.get(app, '#cccccc')  # Fallback to gray if no color

                                        fig.add_trace(go.Bar(
                                            y=subdf['Date'],
                                            x=subdf['DurationHrs_adj'],
                                            base=subdf['HourFloatStart_adj'],
                                            orientation='h',
                                            name=app,
                                            customdata=customdata,
                                            marker=dict(
                                                color=color,
                                                pattern=dict(
                                                    shape="x" if isinstance(app, str) and app.startswith("Meeting_") else "." if app in ["Break", "Desktop"] else "",
                                                    fgcolor="gray",
                                                    solidity=0.2
                                                )
                                            ),
                                            hovertemplate=(
                                                'Date: %{y}<br>'
                                                'Application: %{customdata[2]}<br>'
                                                'Start Time: %{customdata[0]}<br>'
                                                'Duration: %{customdata[1]}<extra></extra>'
                                            )
                                        ))

                                    # Define custom tick values and labels (8 AM to 7 AM next day)
                                    tick_vals = list(range(8, 32))
                                    tick_text = [str(h) if h < 24 else str(h - 24) for h in tick_vals]

                                    # Calculate overall start and end times across all dates
                                    overall_start_time = timeline_data['RealTime'].iloc[0]
                                    overall_end_time = (timeline_data['EndTime'].iloc[-1] + pd.Timedelta(days=1) if timeline_data['EndTime'].iloc[-1].hour < 12 and timeline_data['EndTime'].iloc[-1].hour >= 0 else timeline_data['EndTime'].iloc[-1])  # Adjust date if end time is 12:00 AM to 11:59 AM

                                    # Format the overall start and end times
                                    start_str = overall_start_time.strftime('%B %d - %I:%M %p')
                                    end_str = overall_end_time.strftime('%B %d - %I:%M %p')

                                    # Add a single annotation at the top-left for the entire date range
                                    fig.add_annotation(
                                        x=1,              # Left edge of the figure
                                        y=1,              # Top edge of the figure
                                        xref='paper',     # Relative to the entire figure
                                        yref='paper',     # Relative to the entire figure
                                        text=f'Start: {start_str} | End: {end_str}',
                                        showarrow=False,  # No arrow needed
                                        font=dict(size=14, color='white', family='Arial'),
                                        xanchor='right',   # Align text to the left
                                        yanchor='top',    # Anchor at the top
                                        align='right',
                                        bgcolor='rgba(255,255,255,0.5)'  # Semi-transparent background for visibility
                                    )

                                    date_times = timeline_data.groupby('Date').apply(
                                        lambda x: pd.Series({
                                            'start_time': x['RealTime'].iloc[0],  # First RealTime in the group
                                            'end_time': (x['EndTime'].iloc[-1] + pd.Timedelta(days=1) if x['EndTime'].iloc[-1].hour < 12 and x['EndTime'].iloc[-1].hour >= 0 else x['EndTime'].iloc[-1])
                                        })
                                    ).reset_index()

                                    for _, row in date_times.iterrows():
                                        start_str = row['start_time'].strftime('%B %d - %I:%M %p') if pd.notnull(row['start_time']) else 'N/A'
                                        end_str = row['end_time'].strftime('%B %d - %I:%M %p') if pd.notnull(row['end_time']) else 'N/A'
                                        # Add per-date annotation to the right (optional, adjust x position as needed)
                                        fig.add_annotation(
                                            x=25,  # Position to the right of the chart
                                            y=row['Date'],
                                            text=f'Start: {start_str} | End: {end_str}',
                                            showarrow=False,
                                            font=dict(size=14, color='white', family='Arial'),
                                            xanchor='left',
                                            yanchor='middle'
                                        )

                                    # Format start and end dates for the title
                                    start_date_formated = st.session_state['start_date'].strftime('%B %d, %Y')
                                    end_date_formated = st.session_state['end_date'].strftime('%B %d, %Y')

                                    # Modify the bar height by adjusting the trace
                                    for trace in fig.data:
                                        trace.width = 0.3  # Reduce the width of each bar to make them appear shorter vertically

                                    # Update layout
                                    fig.update_layout(
                                        showlegend=False,
                                        title=dict(
                                            text=f'App Usage Timeline for {start_date_formated} to {end_date_formated}',
                                            font=dict(size=20),
                                            x=0.5,
                                            xanchor='center'
                                        ),
                                        xaxis=dict(
                                            title=dict(
                                                text='Hour of Day',
                                                font=dict(size=18)  # Font properties nested here
                                            ),
                                            range=[8, 31],
                                            tickmode='array',
                                            tickvals=tick_vals,
                                            ticktext=tick_text,
                                            tickfont=dict(size=16),
                                            showgrid=True,
                                            gridcolor='gray',
                                            gridwidth=0.5
                                        ),
                                        yaxis=dict(
                                            title=dict(  # Applying the same fix to yaxis for consistency
                                                text='Date',
                                                font=dict(size=18)
                                            ),
                                            automargin=True,
                                            tickfont=dict(size=16),
                                            categoryorder='array',
                                            categoryarray=sorted(timeline_data['Date'].unique(), reverse=True)
                                        ),
                                        font=dict(family='Arial', size=16, color='white'),
                                        barmode='stack',
                                        template='plotly_dark',
                                        height=600,  # Reduce overall figure height
                                        bargap=0.02,  # Reduce gap between bars
                                        bargroupgap=0.0  # Eliminate gap between bar groups
                                    )

                                    # Display in Streamlit
                                    st.plotly_chart(fig, use_container_width=True)
                                else:
                                    st.write("No data available for the timeline chart.")
                            except Exception as e:
                                st.error(f"Error in Timeline Chart processing: {str(e)}")
                                            
                            # Use st.expander to display the dataframe with custom column widths
                        with st.expander("View Data", expanded=True):
                            st.dataframe(results_rename[['WindowTitle(Task)', 'Application', 'Project', 'Sub-Project', 'Customer', 'Total Time (minutes)', 'Date']], height=350, use_container_width=True)

                        ####For Individual View For all devices####

                        # for device in devices:
                        #     st.header(f"Device: {device}")
                        #     device_results = allDeviceResults[allDeviceResults['Device Name'] == device]
                        #     # Show data for the individual device in an expander
                        #     with st.expander(f"Logs for {device}", expanded=True):
                        #             st.write(device_results)
                            
                    with tab2:
                        # Call the Image Viewer section function
                        imageViewer(combined_log_data)
                else:
                    st.info("No data found")
            else:
                # Handle the case where start_date and/or end_date are not set
                # This could be displaying an error, providing default values, or skipping certain logic
                st.error("Please select a start date and an end date.")
        except Exception as e:
            st.error(f"No data found for selected time period. Please cross check employee name and selected time period.{e}")
    else:
            # Handle the case where start_date and/or end_date are not set
            # This could be displaying an error, providing default values, or skipping certain logic
        st.error("Filters changed. Click 'Apply' to reload data.")

if __name__ == "__main__":
    if st.session_state["authenticated"] and st.session_state["dashboard_user"]:
        if st.session_state["show_report"]:
            show_report_page()
        elif st.session_state["show_leave_report"]:
            show_leave_report_page()
        else:
            show_dashboard(st.session_state["dashboard_user"])
    else:
        _login_screen()