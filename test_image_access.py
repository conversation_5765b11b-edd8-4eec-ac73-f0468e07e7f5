#!/usr/bin/env python3
"""
Test script to verify image file access for the image viewer
"""

import os
import sys
sys.path.append('Sourcecode')

from monDataLoad import load_data_updated
import json
import pandas as pd
from datetime import datetime

def test_image_access():
    print("Testing image file access...")
    
    # Load configuration
    with open("resource/Mon_config.json", "r") as f:
        config = json.load(f)
    
    with open("resource/UserDataNEW.json", "r") as f:
        user_data = json.load(f)
    
    # Find <PERSON><PERSON><PERSON>'s configuration
    harshil_config = None
    for emp_id, emp_data in user_data.items():
        if emp_data.get("EMP_NAME") == "<PERSON><PERSON><PERSON>":
            harshil_config = emp_data
            break
    
    # Set up parameters for data loading
    csv_directory = config["MainPath"][0]
    selected_employee_config = harshil_config
    
    # Load keyword mapping (empty if not found)
    try:
        with open("resource/keyword_mapping.json", "r") as f:
            keyword_mapping = json.load(f)
    except:
        keyword_mapping = {}
    
    # Set date to June 18, 2025
    start_date = datetime(2025, 6, 18)
    end_date = datetime(2025, 6, 18)
    
    try:
        # Call the load_data_updated function
        results, combined_log_data, device_logs = load_data_updated(
            csv_directory=csv_directory,
            selected_employee_config=selected_employee_config,
            keyword_mapping=keyword_mapping,
            start_date=start_date,
            end_date=end_date,
            start_time="13:00",
            end_time="05:00"
        )
        
        print(f"✓ Data loaded successfully!")
        
        if not combined_log_data.empty and 'ImageLocation' in combined_log_data.columns:
            # Get unique image paths
            image_paths = combined_log_data['ImageLocation'].dropna().unique()
            print(f"✓ Found {len(image_paths)} unique image paths")
            
            # Test access to first few images
            accessible_count = 0
            inaccessible_count = 0
            
            for i, img_path in enumerate(image_paths[:10]):  # Test first 10 images
                if os.path.exists(img_path):
                    accessible_count += 1
                    if i < 3:  # Show first 3 accessible paths
                        file_size = os.path.getsize(img_path)
                        print(f"  ✓ Accessible: {img_path} ({file_size} bytes)")
                else:
                    inaccessible_count += 1
                    if i < 3:  # Show first 3 inaccessible paths
                        print(f"  ✗ Not accessible: {img_path}")
            
            print(f"Image accessibility summary:")
            print(f"  Accessible: {accessible_count}")
            print(f"  Inaccessible: {inaccessible_count}")
            
            if accessible_count > 0:
                print("✅ Image viewer should work - images are accessible!")
            else:
                print("❌ Image viewer may not work - no images are accessible!")
                
        else:
            print("✗ No image data found")
            
    except Exception as e:
        print(f"✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_image_access()
